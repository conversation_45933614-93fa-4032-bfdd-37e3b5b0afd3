-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 15, 2025 at 11:23 AM
-- Server version: 8.0.30
-- PHP Version: 8.3.15

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `salozy`
--

-- --------------------------------------------------------

--
-- Table structure for table `appointments`
--

CREATE TABLE `appointments` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `appointment_date` date NOT NULL,
  `appointment_time` time NOT NULL,
  `ticket_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `currency_symbol` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '₹',
  `currency_text` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'INR',
  `staff_id` int DEFAULT NULL,
  `staff_json` json DEFAULT NULL,
  `status` enum('pending','in_progress','completed','cancelled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `appointments`
--

INSERT INTO `appointments` (`id`, `user_id`, `branch_id`, `appointment_date`, `appointment_time`, `ticket_number`, `currency_symbol`, `currency_text`, `staff_id`, `staff_json`, `status`, `notes`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 5, 2, '2025-06-04', '13:15:00', 'APT-20250604-889C4', '₹', 'INR', NULL, NULL, 'completed', 'This is my service listing', '2025-06-04 01:44:37', '2025-06-07 07:01:44', NULL),
(2, 6, 2, '2025-06-04', '14:00:00', 'APT-20250604-B76F6', '₹', 'INR', NULL, NULL, 'completed', 'customer 2', '2025-06-04 02:03:57', '2025-06-19 05:48:13', NULL),
(3, 7, 2, '2025-06-04', '09:00:00', 'APT-20250604-69316', '₹', 'INR', NULL, NULL, 'completed', 'malish i want', '2025-04-07 02:04:32', '2025-06-04 08:18:58', NULL),
(4, 11, 2, '2025-06-20', '12:00:00', 'TKT-34E1305A', '₹', 'INR', NULL, NULL, 'completed', 'test', '2025-06-19 05:21:31', '2025-06-19 05:49:05', NULL),
(5, 17, 2, '2025-06-21', '12:00:00', 'TKT-589D5BB2', '₹', 'INR', NULL, NULL, 'completed', 'test', '2025-06-19 05:44:27', '2025-06-19 19:22:28', NULL),
(6, 18, 2, '2025-06-21', '12:00:00', 'TKT-80E4F73A', '₹', 'INR', NULL, NULL, 'completed', NULL, '2025-06-19 19:12:17', '2025-07-07 02:37:14', NULL),
(7, 21, 7, '2025-06-21', '04:00:00', 'TKT-BD69E614', '₹', 'INR', NULL, NULL, 'pending', 'because my freez hair so special treatment', '2025-06-21 10:07:09', '2025-06-21 10:07:09', NULL),
(8, 11, 2, '2025-07-07', '12:00:00', 'TKT-BE043D4F', '₹', 'INR', NULL, NULL, 'completed', '<EMAIL>', '2025-06-24 01:25:11', '2025-07-09 02:38:10', NULL),
(9, 11, 2, '2025-07-09', '12:00:00', 'TKT-487FC806', '₹', 'INR', NULL, NULL, 'pending', 'tester', '2025-06-24 01:32:34', '2025-07-15 04:25:10', NULL),
(10, 11, 4, '2025-06-24', '12:00:00', 'TKT-87523C75', '₹', 'INR', NULL, NULL, 'completed', 'test', '2025-06-24 02:54:45', '2025-06-24 05:22:06', NULL),
(11, 11, 2, '2025-06-25', '12:00:00', 'TKT-D2978C42', '₹', 'INR', NULL, NULL, 'pending', 'test', '2025-06-24 05:20:33', '2025-07-07 05:23:42', NULL),
(12, 11, 2, '2025-06-24', '12:00:00', 'TKT-099A248C', '₹', 'INR', NULL, NULL, 'completed', 'test', '2025-06-24 05:24:57', '2025-06-24 05:25:43', NULL),
(13, 25, 2, '2025-06-25', '12:00:00', 'APT-20250625-1E35B', '$', 'USD', NULL, NULL, 'pending', 'test', '2025-06-25 05:17:29', '2025-06-25 05:17:29', NULL),
(14, 5, 2, '2025-06-27', '12:00:00', 'APT-20250625-B69BA', '₹', 'INR', NULL, NULL, 'pending', 'grrg', '2025-06-25 05:17:50', '2025-06-26 06:16:45', NULL),
(15, 11, 2, '2025-06-26', '12:00:00', 'TKT-2F0A2C18', '₹', 'INR', NULL, NULL, 'pending', 'test', '2025-06-26 01:41:20', '2025-06-26 01:43:59', '2025-06-26 01:43:59'),
(16, 11, 2, '2025-07-08', '12:00:00', 'TKT-202477FB', '₹', 'INR', NULL, NULL, 'completed', 'sdsdsd', '2025-06-26 01:48:28', '2025-07-07 05:42:58', NULL),
(17, 11, 3, '2025-06-27', '12:00:00', 'TKT-B2D648FB', '₹', 'INR', NULL, NULL, 'pending', 'sdd', '2025-06-26 02:01:39', '2025-06-26 02:01:39', NULL),
(18, 11, 3, '2025-07-15', '12:00:00', 'TKT-26A565F6', '$', 'USD', NULL, NULL, 'completed', 'dsff', '2025-06-26 02:04:13', '2025-07-15 05:21:47', NULL),
(19, 11, 2, '2025-06-26', '12:00:00', 'TKT-A29592E8', '₹', 'INR', NULL, NULL, 'pending', 'test', '2025-06-26 05:46:14', '2025-06-26 05:46:14', NULL),
(20, 11, 2, '2025-07-07', '12:00:00', 'TKT-23483D7F', '₹', 'INR', NULL, NULL, 'cancelled', NULL, '2025-06-26 05:52:24', '2025-07-07 05:33:45', NULL),
(21, 13, 2, '2025-07-07', '12:00:00', 'TKT-45D17698', '₹', 'INR', 11, '{\"id\": 11, \"name\": \"kishan\", \"email\": \"<EMAIL>\", \"phone\": \"**********\"}', 'cancelled', 'test', '2025-06-26 07:53:32', '2025-07-07 04:37:00', NULL),
(22, 11, 4, '2025-06-27', '12:00:00', 'TKT-F4C4E187', '₹', 'INR', 29, '{\"id\": 29, \"name\": \"sahil nu salon staff\", \"email\": \"<EMAIL>\", \"phone\": \"**********\"}', 'pending', 'testing data', '2025-06-27 05:14:20', '2025-06-27 05:14:20', NULL),
(23, 11, 4, '2025-06-27', '12:00:00', 'TKT-C7ACE3E1', '₹', 'INR', NULL, NULL, 'pending', 'tester', '2025-06-27 05:30:52', '2025-06-27 05:39:29', NULL),
(24, 11, 8, '2025-06-29', '12:00:00', 'TKT-306D4442', '₹', 'INR', 30, '{\"id\": 30, \"name\": \"venus\", \"email\": \"<EMAIL>\", \"phone\": \"**********\"}', 'pending', NULL, '2025-06-27 06:06:44', '2025-06-27 06:06:44', NULL),
(25, 38, 7, '2025-07-07', '12:00:00', 'TKT-6D796E99', '₹', 'INR', NULL, NULL, 'pending', 'test', '2025-07-07 01:08:47', '2025-07-07 01:08:47', NULL),
(26, 11, 2, '2025-07-09', '12:00:00', 'TKT-1C1581B0', '₹', 'INR', NULL, NULL, 'pending', NULL, '2025-07-07 05:40:55', '2025-07-08 23:56:25', NULL),
(27, 39, 2, '2025-07-08', '15:00:00', 'TKT-9C4B4B1F', '₹', 'INR', NULL, NULL, 'completed', 'testing', '2025-07-08 06:11:11', '2025-07-08 07:49:22', NULL),
(28, 11, 2, '2025-07-09', '10:00:00', 'TKT-5313979B', '₹', 'INR', NULL, NULL, 'completed', 'test', '2025-07-09 01:21:29', '2025-07-09 01:23:00', NULL),
(29, 11, 2, '2025-07-09', '12:00:00', 'TKT-9B8027D3', '₹', 'INR', NULL, NULL, 'completed', 'test', '2025-07-09 01:30:23', '2025-07-09 01:31:59', NULL),
(30, 11, 2, '2025-07-11', '10:00:00', 'TKT-54A0EA3F', '₹', 'INR', NULL, NULL, 'completed', 'tester', '2025-07-09 02:30:45', '2025-07-09 02:34:54', NULL),
(31, 11, 2, '2025-07-11', '11:00:00', 'TKT-9F9EF3FE', '₹', 'INR', NULL, NULL, 'completed', 'tere', '2025-07-09 02:36:01', '2025-07-09 02:38:57', NULL),
(32, 11, 2, '2025-07-10', '11:00:00', 'TKT-F3092FA1', '₹', 'INR', NULL, NULL, 'pending', 'er', '2025-07-09 02:59:13', '2025-07-09 03:00:27', NULL),
(33, 11, 2, '2025-07-12', '10:00:00', 'TKT-04ABB7E8', '₹', 'INR', NULL, NULL, 'pending', 'tester', '2025-07-09 03:17:15', '2025-07-09 03:17:15', NULL),
(34, 11, 2, '2025-07-10', '12:00:00', 'TKT-44E4EF62', '₹', 'INR', NULL, NULL, 'completed', 'test', '2025-07-09 04:52:16', '2025-07-09 04:53:02', NULL),
(35, 39, 2, '2025-07-15', '10:00:00', 'TKT-F54D029A', '₹', 'INR', NULL, NULL, 'completed', NULL, '2025-07-15 04:18:51', '2025-07-15 04:19:31', NULL),
(36, 39, 2, '2025-07-15', '12:00:00', 'TKT-9ECE5275', '₹', 'INR', NULL, NULL, 'pending', 'test', '2025-07-15 04:59:40', '2025-07-15 04:59:40', NULL),
(37, 27, 2, '2025-07-15', '11:00:00', 'TKT-D478BBED', '₹', 'INR', NULL, NULL, 'pending', 'test', '2025-07-15 05:00:39', '2025-07-15 05:00:39', NULL),
(38, 37, 2, '2025-07-15', '11:00:00', 'TKT-B4E830F8', '₹', 'INR', NULL, NULL, 'pending', 'dsd', '2025-07-15 05:02:19', '2025-07-15 05:02:19', NULL),
(39, 37, 2, '2025-07-15', '10:00:00', 'TKT-345E436C', '₹', 'INR', 39, '{\"id\": 39, \"name\": \"mohan\", \"email\": \"<EMAIL>\", \"phone\": \"**********\"}', 'pending', 'test', '2025-07-15 05:05:36', '2025-07-15 05:05:36', NULL),
(40, 36, 3, '2025-07-15', '10:00:00', 'TKT-6B0FA21C', '$', 'USD', 40, '{\"id\": 40, \"name\": \"kishan\", \"email\": \"<EMAIL>\", \"phone\": \"**********\"}', 'completed', 'test', '2025-07-15 05:06:54', '2025-07-15 05:08:46', NULL),
(41, 27, 3, '2025-07-15', '10:00:00', 'TKT-DC3470A8', '$', 'USD', NULL, NULL, 'pending', 'dfdfdfd', '2025-07-15 05:29:14', '2025-07-15 05:29:14', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `appointment_services`
--

CREATE TABLE `appointment_services` (
  `id` bigint UNSIGNED NOT NULL,
  `appointment_id` bigint UNSIGNED NOT NULL,
  `service_id` bigint UNSIGNED NOT NULL,
  `price` decimal(8,2) DEFAULT '0.00',
  `service_name` varchar(180) COLLATE utf8mb4_unicode_ci NOT NULL,
  `seat_id` bigint UNSIGNED DEFAULT NULL,
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `estimated_end_time` timestamp NULL DEFAULT NULL,
  `plan_used_service` bigint UNSIGNED DEFAULT NULL,
  `service_notes` text COLLATE utf8mb4_unicode_ci,
  `status` enum('pending','in_progress','completed','cancelled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `appointment_services`
--

INSERT INTO `appointment_services` (`id`, `appointment_id`, `service_id`, `price`, `service_name`, `seat_id`, `start_time`, `end_time`, `estimated_end_time`, `plan_used_service`, `service_notes`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 100.00, 'Hair Coloring', 12, NULL, '2025-06-07 07:01:36', NULL, NULL, NULL, 'completed', '2025-06-04 01:44:37', '2025-06-07 07:01:36', NULL),
(2, 1, 7, 75.00, 'malish', 12, NULL, '2025-06-07 07:01:40', NULL, NULL, NULL, 'completed', '2025-06-04 01:44:37', '2025-06-07 07:01:40', NULL),
(3, 1, 3, 40.00, 'Styling', 12, NULL, '2025-06-07 07:01:44', NULL, NULL, NULL, 'completed', '2025-06-04 01:44:37', '2025-06-07 07:01:44', NULL),
(4, 2, 2, 100.00, 'Hair Coloring', 12, NULL, '2025-06-19 05:47:26', NULL, NULL, NULL, 'completed', '2025-06-04 02:03:57', '2025-06-19 05:47:26', NULL),
(5, 3, 7, 75.00, 'malish', 14, '2025-06-04 08:18:48', '2025-06-04 08:18:58', '2025-06-04 08:48:48', NULL, NULL, 'completed', '2025-06-04 02:04:32', '2025-06-04 08:18:58', NULL),
(6, 2, 7, 75.00, 'malish', 12, '2025-06-19 05:47:35', '2025-06-19 05:48:13', '2025-06-19 06:17:35', NULL, NULL, 'completed', '2025-06-04 03:00:25', '2025-06-19 05:48:13', NULL),
(7, 2, 3, 40.00, 'Styling', 12, NULL, '2025-06-19 05:48:08', NULL, NULL, NULL, 'completed', '2025-06-04 03:00:25', '2025-06-19 05:48:08', NULL),
(8, 4, 3, 0.00, 'Styling', NULL, NULL, '2025-06-19 05:49:00', NULL, NULL, NULL, 'completed', '2025-06-19 05:21:31', '2025-06-19 05:49:00', NULL),
(9, 4, 7, 0.00, 'malish', NULL, NULL, '2025-06-19 05:49:05', NULL, NULL, NULL, 'completed', '2025-06-19 05:21:31', '2025-06-19 05:49:05', NULL),
(10, 5, 3, 0.00, 'Styling', 12, NULL, '2025-06-19 19:22:24', NULL, NULL, NULL, 'completed', '2025-06-19 05:44:27', '2025-06-19 19:22:24', NULL),
(11, 5, 7, 0.00, 'malish', 12, NULL, '2025-06-19 19:22:28', NULL, NULL, NULL, 'completed', '2025-06-19 05:44:27', '2025-06-19 19:22:28', NULL),
(12, 6, 2, 0.00, 'Hair Coloring', NULL, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-06-19 19:12:17', '2025-07-07 02:37:14', NULL),
(13, 6, 3, 0.00, 'Styling', NULL, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-06-19 19:12:17', '2025-07-07 02:37:14', NULL),
(14, 6, 7, 0.00, 'malish', NULL, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-06-19 19:12:17', '2025-07-07 02:37:14', NULL),
(15, 7, 11, 0.00, 'Eyebrow', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-21 10:07:09', '2025-06-21 10:07:09', NULL),
(16, 7, 12, 0.00, 'Hair Color', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-21 10:07:09', '2025-06-21 10:07:09', NULL),
(19, 9, 2, 0.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-24 01:32:34', '2025-07-15 04:25:10', NULL),
(21, 10, 14, 0.00, 'Hair color', 16, NULL, '2025-06-24 05:22:06', NULL, NULL, NULL, 'completed', '2025-06-24 02:54:45', '2025-06-24 05:22:06', NULL),
(22, 11, 3, 0.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'in_progress', '2025-06-24 05:20:33', '2025-07-07 05:23:42', NULL),
(23, 11, 7, 0.00, 'malish', 12, NULL, NULL, NULL, NULL, NULL, 'in_progress', '2025-06-24 05:20:33', '2025-07-07 05:23:42', NULL),
(24, 12, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-06-24 05:24:57', '2025-06-24 05:25:43', NULL),
(25, 12, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-06-24 05:24:57', '2025-06-24 05:25:43', NULL),
(26, 8, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-06-25 05:02:09', '2025-07-09 02:38:10', NULL),
(27, 8, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 3, NULL, 'completed', '2025-06-25 05:02:17', '2025-07-09 02:38:10', NULL),
(29, 13, 7, 75.00, 'malish', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-25 05:17:29', '2025-06-25 05:17:29', NULL),
(30, 13, 9, 59.00, 'Wax', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-25 05:17:29', '2025-06-25 05:17:29', NULL),
(31, 14, 9, 59.00, 'Wax', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-25 05:17:50', '2025-06-26 06:16:45', NULL),
(32, 14, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-25 05:17:50', '2025-06-26 06:16:45', NULL),
(33, 14, 7, 75.00, 'malish', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-25 05:17:50', '2025-06-26 06:16:45', NULL),
(34, 9, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 3, NULL, 'pending', '2025-06-25 05:18:17', '2025-07-15 04:25:10', NULL),
(35, 8, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-06-25 05:19:24', '2025-07-09 02:38:10', NULL),
(37, 16, 16, 45.00, 'Hair color', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-06-26 01:48:28', '2025-07-07 05:42:58', NULL),
(38, 17, 16, 45.00, 'Hair color', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-26 02:01:39', '2025-06-26 02:01:39', NULL),
(39, 18, 16, 45.00, 'Hair color', 20, NULL, '2025-07-15 05:21:47', NULL, NULL, NULL, 'completed', '2025-06-26 02:04:13', '2025-07-15 05:21:47', NULL),
(40, 20, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'cancelled', '2025-06-26 05:52:25', '2025-07-07 05:33:45', NULL),
(41, 20, 7, 75.00, 'malish', 12, NULL, NULL, NULL, NULL, NULL, 'cancelled', '2025-06-26 05:52:25', '2025-07-07 05:33:45', NULL),
(42, 21, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'cancelled', '2025-06-26 07:53:32', '2025-07-07 04:37:00', NULL),
(43, 21, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'cancelled', '2025-06-26 07:53:32', '2025-07-07 04:37:00', NULL),
(44, 22, 14, 123.00, 'Hair color', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-27 05:14:20', '2025-06-27 05:14:20', NULL),
(45, 23, 14, 123.00, 'Hair color', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-27 05:30:52', '2025-06-27 05:39:29', NULL),
(46, 24, 17, 1.00, 'hair', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-06-27 06:06:44', '2025-06-27 06:06:44', NULL),
(47, 25, 11, 100.00, 'Eyebrow', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-07 01:08:47', '2025-07-07 01:08:47', NULL),
(49, 26, 9, 59.00, 'Wax', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-07 05:40:55', '2025-07-08 23:56:25', NULL),
(50, 16, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-07 05:42:58', '2025-07-07 05:42:58', NULL),
(51, 27, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-08 06:11:11', '2025-07-08 07:49:22', NULL),
(53, 27, 7, 75.00, 'malish', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-08 07:00:49', '2025-07-08 07:49:22', NULL),
(54, 28, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, 2, NULL, 'completed', '2025-07-09 01:21:29', '2025-07-09 01:23:00', NULL),
(55, 28, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 3, NULL, 'completed', '2025-07-09 01:21:29', '2025-07-09 01:23:00', NULL),
(56, 28, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, 1, NULL, 'completed', '2025-07-09 01:22:06', '2025-07-09 01:23:00', NULL),
(57, 29, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-09 01:30:23', '2025-07-09 01:31:59', NULL),
(58, 29, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 3, NULL, 'completed', '2025-07-09 01:30:23', '2025-07-09 01:31:59', NULL),
(59, 29, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-09 01:30:23', '2025-07-09 01:31:59', NULL),
(60, 30, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-09 02:30:45', '2025-07-09 02:34:54', NULL),
(61, 30, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 3, NULL, 'completed', '2025-07-09 02:30:45', '2025-07-09 02:34:54', NULL),
(62, 30, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-09 02:30:45', '2025-07-09 02:34:54', NULL),
(63, 31, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-09 02:36:01', '2025-07-09 02:38:57', NULL),
(64, 31, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 3, NULL, 'completed', '2025-07-09 02:36:01', '2025-07-09 02:38:57', NULL),
(65, 31, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-09 02:36:01', '2025-07-09 02:38:57', NULL),
(66, 32, 2, 100.00, 'Hair Coloring', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-09 02:59:13', '2025-07-09 03:00:27', NULL),
(67, 32, 3, 40.00, 'Styling', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-09 02:59:13', '2025-07-09 03:00:27', NULL),
(69, 32, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 3, NULL, 'pending', '2025-07-09 02:59:55', '2025-07-09 03:00:27', NULL),
(70, 32, 9, 59.00, 'Wax', 12, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-09 03:00:27', '2025-07-09 03:00:27', NULL),
(71, 33, 3, 40.00, 'Styling', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-09 03:17:15', '2025-07-09 03:17:15', NULL),
(72, 33, 2, 100.00, 'Hair Coloring', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-09 03:17:15', '2025-07-09 03:17:15', NULL),
(73, 34, 2, 50.00, 'Hair Coloring', 12, NULL, NULL, NULL, 14, NULL, 'completed', '2025-07-09 04:52:16', '2025-07-09 04:53:02', NULL),
(74, 34, 3, 20.00, 'Styling', 12, NULL, NULL, NULL, 15, NULL, 'completed', '2025-07-09 04:52:16', '2025-07-09 04:53:02', NULL),
(75, 34, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 3, NULL, 'completed', '2025-07-09 04:52:16', '2025-07-09 04:53:02', NULL),
(76, 35, 3, 20.00, 'Styling', 12, NULL, NULL, NULL, 26, NULL, 'completed', '2025-07-15 04:18:51', '2025-07-15 04:19:31', NULL),
(77, 35, 7, 75.00, 'malish', 12, NULL, NULL, NULL, 27, NULL, 'completed', '2025-07-15 04:18:51', '2025-07-15 04:19:31', NULL),
(78, 36, 3, 20.00, 'Styling', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-15 04:59:41', '2025-07-15 04:59:41', NULL),
(79, 36, 7, 75.00, 'malish', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-15 04:59:41', '2025-07-15 04:59:41', NULL),
(80, 37, 3, 20.00, 'Styling', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-15 05:00:39', '2025-07-15 05:00:39', NULL),
(81, 37, 7, 75.00, 'malish', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-15 05:00:39', '2025-07-15 05:00:39', NULL),
(82, 38, 3, 20.00, 'Styling', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-15 05:02:19', '2025-07-15 05:02:19', NULL),
(83, 39, 3, 20.00, 'Styling', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-15 05:05:36', '2025-07-15 05:05:36', NULL),
(84, 40, 16, 45.00, 'Hair color', 20, NULL, NULL, NULL, NULL, NULL, 'completed', '2025-07-15 05:06:54', '2025-07-15 05:08:46', NULL),
(85, 41, 16, 45.00, 'Hair color', NULL, NULL, NULL, NULL, NULL, NULL, 'pending', '2025-07-15 05:29:14', '2025-07-15 05:29:14', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE `branches` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `social_links` json DEFAULT NULL,
  `currency_symbol` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '₹',
  `currency_text` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'INR',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `allow_staff` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `branch_user_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `branches`
--

INSERT INTO `branches` (`id`, `user_id`, `name`, `address`, `email`, `phone`, `logo`, `social_links`, `currency_symbol`, `currency_text`, `is_active`, `allow_staff`, `created_at`, `updated_at`, `deleted_at`, `branch_user_id`) VALUES
(1, 1, 'Main Branch', '123 Main Street', NULL, '********10', NULL, NULL, '₹', 'INR', 1, 0, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL, 1),
(2, 8, 'Bonanza', 'Sadhuwaswani road', '<EMAIL>', '**********', NULL, '[{\"url\": \"https://www.facebook.com/\", \"icon\": \"facebook\"}, {\"url\": \"https://x.com/\", \"icon\": \"x\"}, {\"url\": \"https://www.instagram.com/\", \"icon\": \"instagram\"}]', '₹', 'INR', 1, 1, '2025-06-02 02:17:21', '2025-07-15 05:05:07', NULL, 35),
(3, 8, 'Glam Haven', 'Khodiyar nagar main road', '<EMAIL>', '34343343434', NULL, '[]', '$', 'USD', 1, 0, '2025-06-03 04:27:59', '2025-07-04 02:55:48', NULL, 36),
(4, 13, 'Sahil nu salon', 'test', '<EMAIL>', '**********', NULL, '[]', '₹', 'INR', 1, 1, '2025-06-13 10:52:04', '2025-06-27 06:06:25', NULL, 13),
(5, 14, 'Dipesh', NULL, NULL, NULL, NULL, NULL, '₹', 'INR', 1, 0, '2025-06-19 05:23:13', '2025-06-19 05:23:13', NULL, 14),
(6, 15, 'Kevin Salon', NULL, NULL, NULL, NULL, NULL, '₹', 'INR', 1, 0, '2025-06-19 05:30:50', '2025-06-19 05:30:50', NULL, 15),
(7, 19, 'Keshvi', 'Omnis dolore volupta', '<EMAIL>', '+1 (313) 219-6751', NULL, NULL, '₹', 'INR', 1, 0, '2025-06-21 09:28:10', '2025-06-21 09:41:11', NULL, 19),
(8, 13, 'kishan', '122210 Abc Av, Norwalk', NULL, '**********', NULL, '[]', '₹', 'INR', 1, 1, '2025-06-27 02:03:16', '2025-06-27 06:46:17', NULL, 13),
(9, 13, 'bla', 'bla', NULL, '**********', NULL, '[]', '₹', 'INR', 1, 0, '2025-06-27 02:20:22', '2025-06-27 02:39:00', '2025-06-27 02:39:00', 13),
(10, 8, 'krunal', 'krunal road', '<EMAIL>', '8555554555', NULL, '[]', '₹', 'INR', 1, 1, '2025-07-04 03:27:33', '2025-07-04 03:30:37', NULL, 37);

-- --------------------------------------------------------

--
-- Table structure for table `branch_media`
--

CREATE TABLE `branch_media` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('gallery','slider') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `branch_media`
--

INSERT INTO `branch_media` (`id`, `branch_id`, `image`, `type`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(8, 7, 'branch_media/JV95xlm94pBuWv8OYIvEpOYnYXRy2Sf12YMUvbqP.jpg', 'slider', 1, '2025-06-21 09:55:25', '2025-06-21 09:55:25', NULL),
(10, 2, 'branch_media/gsfry4ss8J0lTYIulDytC3tsGZZavceQXJZw1xSW.jpg', 'gallery', 1, '2025-06-24 00:08:35', '2025-06-24 00:08:35', NULL),
(11, 2, 'branch_media/bgeT1RFuzI0GO3nyblc5FgTmLzhbAFwYvqqvK8Ln.jpg', 'gallery', 1, '2025-06-24 00:08:35', '2025-07-07 06:22:24', NULL),
(14, 2, 'branch_media/PS7y4H6ilJ3oLfzGjYoRxH6Kgo2goQ38dZ5zVqpK.jpg', 'slider', 1, '2025-06-24 00:08:58', '2025-06-24 00:08:58', NULL),
(15, 2, 'branch_media/ZKqgDw5LxZ2fozOgtZiAqn9f1S61lhNukTLbjs6Y.jpg', 'slider', 1, '2025-06-24 00:08:58', '2025-06-26 06:15:55', NULL),
(16, 2, 'branch_media/55ldJrbqAzVi2YUiGUtgfkk8Cr9Zgqdy9ebxLQRD.jpg', 'slider', 1, '2025-06-24 00:08:58', '2025-06-24 00:08:58', NULL),
(17, 2, 'branch_media/vKjmyNQ1BJzZImCaYDIUKYJqQb2FTU13Grdz43mU.jpg', 'gallery', 1, '2025-07-07 06:23:46', '2025-07-07 06:23:46', NULL),
(18, 2, 'branch_media/PzEQQKSR69RpssK9wsO6Mq3r0ezwlpIwdDvQoNXi.jpg', 'gallery', 1, '2025-07-07 06:23:46', '2025-07-07 06:23:46', NULL),
(19, 2, 'branch_media/mvOGw8pnKfdO8U80mEpxLIvnNPmJWkiwqO0aUsrM.jpg', 'gallery', 1, '2025-07-07 06:23:46', '2025-07-07 06:23:46', NULL),
(20, 2, 'branch_media/i8x2trszQ9IOCxZeguS7QRJgBnIzdzoXVY4nsVbK.jpg', 'gallery', 1, '2025-07-07 06:23:47', '2025-07-07 06:23:47', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `domains`
--

CREATE TABLE `domains` (
  `id` int UNSIGNED NOT NULL,
  `domain` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `domains`
--

INSERT INTO `domains` (`id`, `domain`, `tenant_id`, `created_at`, `updated_at`) VALUES
(1, 'bonanza.salozy.test', 'bonanza', '2025-06-02 02:17:21', '2025-06-02 02:17:21'),
(2, 'sahil.salozy.test', 'sahil', '2025-06-13 10:52:04', '2025-06-13 10:52:04'),
(3, 'dipesh.salozy.test', 'dipesh', '2025-06-19 05:23:13', '2025-06-19 05:23:13'),
(4, 'kevin.salozy.test', 'kevin', '2025-06-19 05:30:50', '2025-06-19 05:30:50'),
(5, 'keshvi.salozy.test', 'Keshvi', '2025-06-21 09:28:10', '2025-06-21 09:28:10');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `queue` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(20, '0001_01_01_000000_create_users_table', 1),
(21, '0001_01_01_000001_create_cache_table', 1),
(22, '0001_01_01_000002_create_jobs_table', 1),
(23, '2019_09_15_000010_create_tenants_table', 1),
(24, '2019_09_15_000020_create_domains_table', 1),
(25, '2025_05_01_000001_create_branches_table', 1),
(26, '2025_05_12_131051_create_permission_tables', 1),
(27, '2025_05_13_080436_create_personal_access_tokens_table', 1),
(28, '2025_05_15_000001_create_services_table', 1),
(29, '2025_05_20_000001_create_seats_table', 1),
(30, '2025_05_20_000002_create_appointments_table', 1),
(31, '2025_05_20_000003_create_appointment_services_table', 1),
(33, '2025_05_26_083825_add_company_domain_to_users_table', 1),
(34, '2025_05_26_093047_add_tenant_user_id_to_users_table', 1),
(35, '2025_05_26_094442_remove_tenant_user_id_from_users_table', 1),
(36, '2025_05_27_000001_create_subscription_plans_table', 1),
(37, '2025_05_27_000002_create_vendor_subscriptions_table', 1),
(38, '2025_06_01_000001_create_terms_conditions_table', 1),
(39, '2024_03_21_000001_create_plans_table', 2),
(40, '2024_03_21_000002_create_plan_services_table', 2),
(41, '2025_06_10_000001_create_vendor_customers_table', 3),
(42, '2025_05_20_000005_create_working_hours_table', 4),
(43, '2025_07_14_115121_create_payment_methods_table', 5),
(44, '2025_07_15_063938_create_plan_transactions_table', 6),
(45, '2025_07_15_064018_add_transaction_id_to_vendor_subscriptions_table', 6),
(46, '2025_07_15_065717_remove_unique_active_subscription_constraint_from_vendor_subscriptions', 7),
(47, '2024_03_22_000001_create_plan_usages_table', 1),
(48, '2025_06_05_000001_create_branch_media_table', 1),
(49, '2025_07_15_084406_drop_unique_active_subscription_index', 8);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(3, 'App\\Models\\User', 2),
(3, 'App\\Models\\User', 3),
(3, 'App\\Models\\User', 4),
(4, 'App\\Models\\User', 5),
(4, 'App\\Models\\User', 6),
(4, 'App\\Models\\User', 7),
(2, 'App\\Models\\User', 8),
(4, 'App\\Models\\User', 8),
(5, 'App\\Models\\User', 8),
(3, 'App\\Models\\User', 9),
(3, 'App\\Models\\User', 11),
(4, 'App\\Models\\User', 11),
(2, 'App\\Models\\User', 14),
(2, 'App\\Models\\User', 15),
(4, 'App\\Models\\User', 18),
(2, 'App\\Models\\User', 19),
(3, 'App\\Models\\User', 20),
(4, 'App\\Models\\User', 21),
(4, 'App\\Models\\User', 22),
(4, 'App\\Models\\User', 23),
(4, 'App\\Models\\User', 24),
(4, 'App\\Models\\User', 25),
(4, 'App\\Models\\User', 26),
(4, 'App\\Models\\User', 27),
(4, 'App\\Models\\User', 28),
(3, 'App\\Models\\User', 29),
(3, 'App\\Models\\User', 30),
(5, 'App\\Models\\User', 32),
(5, 'App\\Models\\User', 33),
(5, 'App\\Models\\User', 35),
(4, 'App\\Models\\User', 36),
(5, 'App\\Models\\User', 36),
(4, 'App\\Models\\User', 37),
(5, 'App\\Models\\User', 37),
(4, 'App\\Models\\User', 38),
(3, 'App\\Models\\User', 39),
(4, 'App\\Models\\User', 39),
(3, 'App\\Models\\User', 40);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`email`, `token`, `created_at`) VALUES
('<EMAIL>', '$2y$10$N4s96vjXoDjkTfwt91fENulygo5TihSBI1baIr2BA.OSP5Ar1kD4K', '2025-06-25 01:42:31');

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mode` enum('live','test') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'test',
  `details` json DEFAULT NULL,
  `status` enum('active','deactive') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `name`, `type`, `mode`, `details`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES
(5, 'Razorpay', 'razorpay', 'test', '{\"key_id\": \"rzp_test_hZuuofrr9TgeGh\", \"key_secret\": \"SoovqeGLM53tj8xhvRnSBfdY\"}', 'active', NULL, '2025-07-14 07:17:34', '2025-07-15 00:08:49');

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'view branches', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(2, 'create branches', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(3, 'edit branches', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(4, 'delete branches', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(5, 'view staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(6, 'create staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(7, 'edit staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(8, 'delete staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(9, 'view services', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(10, 'create services', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(11, 'edit services', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(12, 'delete services', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(13, 'view seats', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(14, 'create seats', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(15, 'edit seats', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(16, 'delete seats', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(17, 'view appointments', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(18, 'create appointments', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(19, 'edit appointments', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(20, 'delete appointments', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(21, 'view terms', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(22, 'create terms', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(23, 'edit terms', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(24, 'delete terms', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(25, 'view vendors', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(26, 'create vendors', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(27, 'edit vendors', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(28, 'delete vendors', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(29, 'view working-hours', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(30, 'edit working-hours', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55');

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plans`
--

CREATE TABLE `plans` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `validity_days` int NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plans`
--

INSERT INTO `plans` (`id`, `branch_id`, `name`, `price`, `validity_days`, `description`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 2, 'Gold Package', 1500.00, 30, NULL, 'active', '2025-06-06 02:43:59', '2025-06-07 00:25:31', NULL),
(2, 2, 'Silver Package', 1500.00, 365, 'Lorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data a', 'active', '2025-06-12 02:33:53', '2025-06-12 02:33:53', NULL),
(3, 2, 'Platinum package', 4585.00, 485, 'Lorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data a', 'active', '2025-06-12 02:34:28', '2025-06-12 02:34:28', NULL),
(4, 2, 'Children plan', 45855.00, 458, 'Lorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data aLorem ipsum data a', 'inactive', '2025-06-12 03:28:47', '2025-06-12 03:29:33', NULL),
(5, 3, 'Gold plan', 123.00, 60, 'Unique service', 'active', '2025-06-19 06:34:20', '2025-06-19 06:49:38', NULL),
(6, 7, 'Bridal', 18000.00, 2, 'Bridal Makeup (HD)\n\nHair Styling (1 Style)\n\nSaree/Dress Draping\n\nBasic Facial\n\nNail Shaping + Polish\n\nLight Eye Makeup\n\nJewelry Setting Assistance', 'active', '2025-06-21 09:46:18', '2025-06-21 09:46:18', NULL),
(7, 7, 'Bridal Redy', 25000.00, 3, 'Bridal Makeup (HD/Airbrush)\n\nAdvanced Hair Styling (2 Styles)\n\nPre-Bridal Cleanup\n\nFace & Body Waxing\n\nManicure + Pedicure (Normal)\n\nSaree/Dress Draping\n\nEyelash Extension (Temporary)\n\nJewelry & Dupatta Setting', 'active', '2025-06-21 09:47:37', '2025-06-21 09:47:37', NULL),
(8, 7, 'Luxury Bridal Package', 35000.00, 3, 'Celebrity/High-Definition Airbrush Makeup\n\nHair Trial + Final Hairdo\n\nBridal Facial (Gold/Diamond)\n\nSpa Manicure & Pedicure\n\nFull Body Polishing\n\nSaree/Dress Draping\n\nCustom Hair Accessories Setting\n\nHD Photography-Ready Look', 'active', '2025-06-21 09:48:12', '2025-06-21 09:48:12', NULL),
(9, 2, 'Hello', 25.00, 25, 'Test', 'active', '2025-06-21 10:27:17', '2025-06-21 10:27:27', '2025-06-21 10:27:27'),
(10, 2, 'Test', 5.00, 5, 'Testing', 'active', '2025-06-21 10:27:55', '2025-06-21 10:28:14', '2025-06-21 10:28:14');

-- --------------------------------------------------------

--
-- Table structure for table `plan_services`
--

CREATE TABLE `plan_services` (
  `id` bigint UNSIGNED NOT NULL,
  `plan_id` bigint UNSIGNED NOT NULL,
  `service_id` bigint UNSIGNED NOT NULL,
  `allowed_count` int NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plan_services`
--

INSERT INTO `plan_services` (`id`, `plan_id`, `service_id`, `allowed_count`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 3, '2025-06-06 02:43:59', '2025-06-06 02:43:59', NULL),
(2, 1, 3, 2, '2025-06-06 02:43:59', '2025-06-06 02:43:59', NULL),
(3, 1, 7, 5, '2025-06-06 02:43:59', '2025-06-06 02:43:59', NULL),
(4, 2, 2, 5, '2025-06-12 02:33:53', '2025-06-12 02:33:53', NULL),
(5, 2, 3, 8, '2025-06-12 02:33:53', '2025-06-12 02:33:53', NULL),
(6, 2, 7, 8, '2025-06-12 02:33:53', '2025-06-12 02:33:53', NULL),
(7, 3, 2, 85, '2025-06-12 02:34:28', '2025-06-12 02:34:28', NULL),
(8, 3, 3, 8, '2025-06-12 02:34:28', '2025-06-12 02:34:28', NULL),
(9, 3, 7, 8, '2025-06-12 02:34:28', '2025-06-12 02:34:28', NULL),
(10, 4, 2, 4, '2025-06-12 03:28:47', '2025-06-12 03:28:47', NULL),
(11, 4, 3, 5, '2025-06-12 03:28:47', '2025-06-12 03:28:47', NULL),
(12, 4, 7, 5, '2025-06-12 03:28:47', '2025-06-12 03:28:47', NULL),
(14, 6, 10, 1, '2025-06-21 09:46:18', '2025-06-21 09:46:54', NULL),
(15, 6, 11, 1, '2025-06-21 09:46:18', '2025-06-21 09:46:54', NULL),
(16, 6, 12, 1, '2025-06-21 09:46:18', '2025-06-21 09:46:54', NULL),
(17, 6, 13, 1, '2025-06-21 09:46:54', '2025-06-21 09:46:54', NULL),
(18, 7, 10, 1, '2025-06-21 09:47:37', '2025-06-21 09:47:37', NULL),
(19, 7, 11, 1, '2025-06-21 09:47:37', '2025-06-21 09:47:37', NULL),
(20, 7, 12, 1, '2025-06-21 09:47:37', '2025-06-21 09:47:37', NULL),
(21, 7, 13, 1, '2025-06-21 09:47:37', '2025-06-21 09:47:37', NULL),
(22, 8, 10, 2, '2025-06-21 09:48:12', '2025-06-21 09:48:12', NULL),
(23, 8, 11, 2, '2025-06-21 09:48:12', '2025-06-21 09:48:12', NULL),
(24, 8, 12, 1, '2025-06-21 09:48:12', '2025-06-21 09:48:12', NULL),
(25, 8, 13, 3, '2025-06-21 09:48:12', '2025-06-21 09:48:12', NULL),
(26, 9, 2, 5, '2025-06-21 10:27:17', '2025-06-21 10:27:17', NULL),
(27, 9, 3, 8, '2025-06-21 10:27:17', '2025-06-21 10:27:17', NULL),
(28, 9, 7, 9, '2025-06-21 10:27:17', '2025-06-21 10:27:17', NULL),
(29, 9, 9, 3, '2025-06-21 10:27:17', '2025-06-21 10:27:17', NULL),
(30, 10, 2, 8, '2025-06-21 10:27:55', '2025-06-21 10:27:55', NULL),
(31, 5, 16, 4, '2025-06-25 08:26:38', '2025-06-25 08:26:38', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `plan_service_usages`
--

CREATE TABLE `plan_service_usages` (
  `id` bigint UNSIGNED NOT NULL,
  `plan_usage_id` bigint UNSIGNED NOT NULL,
  `service_id` bigint UNSIGNED NOT NULL,
  `service_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `appointment_id` bigint UNSIGNED DEFAULT NULL,
  `used_count` int NOT NULL DEFAULT '0',
  `remaining_count` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plan_service_usages`
--

INSERT INTO `plan_service_usages` (`id`, `plan_usage_id`, `service_id`, `service_name`, `appointment_id`, `used_count`, `remaining_count`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 'Hair Coloring', NULL, 1, 0, '2025-06-19 05:20:57', '2025-07-09 01:23:00', NULL),
(2, 1, 3, 'Styling', NULL, 1, 0, '2025-06-19 05:20:57', '2025-07-09 01:23:00', NULL),
(3, 1, 7, 'malish', NULL, 6, 2, '2025-06-19 05:20:57', '2025-07-09 04:53:02', NULL),
(4, 2, 2, 'Hair Coloring', NULL, 0, 5, '2025-06-19 05:42:26', '2025-06-19 05:42:26', NULL),
(5, 2, 3, 'Styling', NULL, 0, 8, '2025-06-19 05:42:26', '2025-06-19 05:42:26', NULL),
(6, 2, 7, 'malish', NULL, 0, 8, '2025-06-19 05:42:26', '2025-06-19 05:42:26', NULL),
(7, 3, 16, 'Hair color', NULL, 0, 4, '2025-06-26 02:04:41', '2025-06-26 02:04:41', NULL),
(8, 4, 2, 'Hair Coloring', NULL, 0, 85, '2025-06-26 06:04:13', '2025-06-26 06:04:13', NULL),
(9, 4, 3, 'Styling', NULL, 0, 8, '2025-06-26 06:04:13', '2025-06-26 06:04:13', NULL),
(10, 4, 7, 'malish', NULL, 0, 8, '2025-06-26 06:04:13', '2025-06-26 06:04:13', NULL),
(11, 5, 2, 'Hair Coloring', NULL, 0, 5, '2025-06-26 06:04:50', '2025-06-26 06:04:50', NULL),
(12, 5, 3, 'Styling', NULL, 0, 8, '2025-06-26 06:04:50', '2025-06-26 06:04:50', NULL),
(13, 5, 7, 'malish', NULL, 0, 8, '2025-06-26 06:04:50', '2025-06-26 06:04:50', NULL),
(14, 6, 2, 'Hair Coloring', NULL, 1, 4, '2025-06-26 06:13:37', '2025-07-09 04:53:02', NULL),
(15, 6, 3, 'Styling', NULL, 1, 7, '2025-06-26 06:13:37', '2025-07-09 04:53:02', NULL),
(16, 6, 7, 'malish', NULL, 3, 0, '2025-06-26 06:13:37', '2025-06-26 06:13:37', NULL),
(17, 7, 10, 'Hair Cutting', NULL, 0, 2, '2025-07-07 01:10:22', '2025-07-07 01:10:22', NULL),
(18, 7, 11, 'Eyebrow', NULL, 0, 2, '2025-07-07 01:10:22', '2025-07-07 01:10:22', NULL),
(19, 7, 12, 'Hair Color', NULL, 0, 1, '2025-07-07 01:10:22', '2025-07-07 01:10:22', NULL),
(20, 7, 13, 'Makeup (HD)', NULL, 0, 3, '2025-07-07 01:10:22', '2025-07-07 01:10:22', NULL),
(21, 8, 10, 'Hair Cutting', NULL, 0, 2, '2025-07-07 01:12:40', '2025-07-07 01:12:40', NULL),
(22, 8, 11, 'Eyebrow', NULL, 0, 2, '2025-07-07 01:12:40', '2025-07-07 01:12:40', NULL),
(23, 8, 12, 'Hair Color', NULL, 0, 1, '2025-07-07 01:12:40', '2025-07-07 01:12:40', NULL),
(24, 8, 13, 'Makeup (HD)', NULL, 0, 3, '2025-07-07 01:12:40', '2025-07-07 01:12:40', NULL),
(25, 9, 2, 'Hair Coloring', NULL, 0, 5, '2025-07-08 06:03:15', '2025-07-08 06:03:15', NULL),
(26, 9, 3, 'Styling', NULL, 2, 6, '2025-07-08 06:03:15', '2025-07-15 04:19:31', NULL),
(27, 9, 7, 'malish', NULL, 2, 6, '2025-07-08 06:03:15', '2025-07-15 04:19:31', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `plan_transactions`
--

CREATE TABLE `plan_transactions` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `subscription_plan_id` bigint UNSIGNED NOT NULL,
  `payment_method_id` bigint UNSIGNED NOT NULL,
  `razorpay_order_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `razorpay_payment_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `razorpay_signature` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `razorpay_order_data` json DEFAULT NULL,
  `razorpay_payment_data` json DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','completed','failed','cancelled') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `payment_status` enum('created','authorized','captured','refunded','failed') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'created',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `paid_at` timestamp NULL DEFAULT NULL,
  `failed_at` timestamp NULL DEFAULT NULL,
  `failure_reason` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plan_transactions`
--

INSERT INTO `plan_transactions` (`id`, `user_id`, `subscription_plan_id`, `payment_method_id`, `razorpay_order_id`, `razorpay_payment_id`, `razorpay_signature`, `razorpay_order_data`, `razorpay_payment_data`, `amount`, `currency`, `status`, `payment_status`, `notes`, `paid_at`, `failed_at`, `failure_reason`, `created_at`, `updated_at`) VALUES
(3, 8, 6, 5, 'order_QtHBepdwAUYyo9', 'pay_QtHBmUnClNSkm4', '9183f6427c9cb4683b21a608bf8fbf9c0c67f396a8ba1370dcef9ffe4ae708b0', '{\"id\": \"order_QtHBepdwAUYyo9\", \"notes\": {\"plan_id\": 6, \"plan_name\": \"Professional (Yearly)\"}, \"amount\": 100, \"entity\": \"order\", \"status\": \"created\", \"receipt\": \"subscription_6_1752567886\", \"attempts\": 0, \"currency\": \"INR\", \"offer_id\": null, \"amount_due\": 100, \"created_at\": **********, \"amount_paid\": 0}', '{\"id\": \"pay_QtHBmUnClNSkm4\", \"fee\": 2, \"tax\": 0, \"upi\": {\"vpa\": \"test@okaxis\"}, \"vpa\": \"test@okaxis\", \"bank\": null, \"email\": \"<EMAIL>\", \"notes\": {\"plan_id\": 6, \"plan_name\": \"Professional (Yearly)\"}, \"amount\": 100, \"entity\": \"payment\", \"method\": \"upi\", \"status\": \"captured\", \"wallet\": null, \"card_id\": null, \"contact\": \"+********\", \"captured\": true, \"currency\": \"INR\", \"order_id\": \"order_QtHBepdwAUYyo9\", \"created_at\": **********, \"error_code\": null, \"error_step\": null, \"invoice_id\": null, \"description\": \"Professional (Yearly) Subscription\", \"error_reason\": null, \"error_source\": null, \"acquirer_data\": {\"rrn\": \"************\", \"upi_transaction_id\": \"2688CD5708B9F0AA1870D1165A76BF40\"}, \"international\": false, \"refund_status\": null, \"amount_refunded\": 0, \"error_description\": null}', 1.00, 'INR', 'completed', 'captured', 'Payment completed successfully via Razorpay', '2025-07-15 02:55:14', NULL, NULL, '2025-07-15 02:54:47', '2025-07-15 02:55:14');

-- --------------------------------------------------------

--
-- Table structure for table `plan_usages`
--

CREATE TABLE `plan_usages` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `plan_id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `currency_symbol` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '₹',
  `currency_text` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'INR',
  `purchased_at` timestamp NOT NULL,
  `expires_at` timestamp NOT NULL,
  `status` enum('active','expired','cancelled','pending') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plan_usages`
--

INSERT INTO `plan_usages` (`id`, `user_id`, `plan_id`, `branch_id`, `currency_symbol`, `currency_text`, `purchased_at`, `expires_at`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 11, 3, 2, '₹', 'INR', '2025-06-19 05:20:57', '2026-10-17 05:20:57', 'active', '2025-06-19 05:20:57', '2025-06-24 05:26:18', NULL),
(2, 16, 2, 2, '₹', 'INR', '2025-06-19 05:42:26', '2026-06-19 05:42:26', 'pending', '2025-06-19 05:42:26', '2025-06-19 05:42:26', NULL),
(3, 28, 5, 3, '$', 'USD', '2025-06-26 02:04:41', '2025-08-25 02:04:41', 'pending', '2025-06-26 02:04:41', '2025-06-26 02:04:41', NULL),
(4, 8, 3, 2, '₹', 'INR', '2025-06-26 06:04:13', '2026-10-24 06:04:13', 'pending', '2025-06-26 06:04:13', '2025-06-26 06:04:13', NULL),
(5, 8, 2, 2, '₹', 'INR', '2025-06-26 06:04:50', '2026-06-26 06:04:50', 'pending', '2025-06-26 06:04:50', '2025-06-26 06:04:50', NULL),
(6, 11, 2, 2, '₹', 'INR', '2025-06-26 06:13:37', '2026-06-26 06:13:37', 'active', '2025-06-26 06:13:37', '2025-07-09 04:48:28', NULL),
(7, 38, 8, 7, '₹', 'INR', '2025-07-07 01:10:22', '2025-07-10 01:10:22', 'pending', '2025-07-07 01:10:22', '2025-07-07 01:10:22', NULL),
(8, 38, 8, 7, '₹', 'INR', '2025-07-07 01:12:40', '2025-07-10 01:12:40', 'pending', '2025-07-07 01:12:40', '2025-07-07 01:12:40', NULL),
(9, 39, 2, 2, '₹', 'INR', '2025-07-08 06:03:15', '2026-07-08 06:03:15', 'active', '2025-07-08 06:03:15', '2025-07-08 06:03:32', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(2, 'vendor', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(3, 'staff', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(4, 'customer', 'web', '2025-06-02 02:10:55', '2025-06-02 02:10:55'),
(5, 'branchuser', 'web', '2025-07-04 01:58:31', '2025-07-04 01:58:31');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `role_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(1, 1),
(2, 1),
(3, 1),
(4, 1),
(5, 1),
(6, 1),
(7, 1),
(8, 1),
(9, 1),
(10, 1),
(11, 1),
(12, 1),
(13, 1),
(14, 1),
(15, 1),
(16, 1),
(17, 1),
(18, 1),
(19, 1),
(20, 1),
(21, 1),
(22, 1),
(23, 1),
(24, 1),
(25, 1),
(26, 1),
(27, 1),
(28, 1),
(29, 1),
(30, 1),
(1, 2),
(2, 2),
(3, 2),
(4, 2),
(5, 2),
(6, 2),
(7, 2),
(8, 2),
(9, 2),
(10, 2),
(11, 2),
(12, 2),
(13, 2),
(14, 2),
(15, 2),
(16, 2),
(17, 2),
(18, 2),
(19, 2),
(20, 2),
(21, 2),
(22, 2),
(23, 2),
(24, 2),
(29, 2),
(30, 2),
(9, 3),
(13, 3),
(17, 3),
(18, 3),
(19, 3),
(9, 4),
(17, 4),
(18, 4);

-- --------------------------------------------------------

--
-- Table structure for table `seats`
--

CREATE TABLE `seats` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `staff_id` bigint UNSIGNED DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `status` enum('available','occupied','cleaning','maintenance') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'available',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `seats`
--

INSERT INTO `seats` (`id`, `branch_id`, `staff_id`, `name`, `notes`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 'Chair 1', NULL, 'available', '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(2, 1, 3, 'Chair 2', NULL, 'available', '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(3, 1, 4, 'Chair 3', NULL, 'occupied', '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(4, 1, NULL, 'Chair 4', NULL, 'maintenance', '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(12, 2, 39, 'A1', 'fdg', 'available', '2025-06-02 05:17:51', '2025-07-07 03:11:28', NULL),
(14, 2, NULL, 'A3', '122', 'available', '2025-06-02 05:25:15', '2025-07-09 04:08:39', NULL),
(15, 2, NULL, 'A4', '1212', 'available', '2025-06-02 05:25:32', '2025-07-09 04:08:42', NULL),
(16, 4, NULL, 'Dipesh', NULL, 'available', '2025-06-19 19:14:50', '2025-06-19 19:14:50', NULL),
(17, 7, 20, 'Seat 1', NULL, 'available', '2025-06-21 09:52:11', '2025-06-21 09:52:11', NULL),
(19, 2, 11, 'A2', NULL, 'available', '2025-07-09 04:15:49', '2025-07-09 04:15:49', NULL),
(20, 3, 40, 'A1', 'tester', 'available', '2025-07-15 05:08:35', '2025-07-15 05:08:35', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `gender` enum('male','female','other') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'male',
  `reminder_after_service` int NOT NULL DEFAULT '0',
  `duration_minutes` int NOT NULL,
  `price` decimal(8,2) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `branch_id`, `name`, `description`, `gender`, `reminder_after_service`, `duration_minutes`, `price`, `is_active`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 'Haircut', 'Basic haircut service', 'male', 0, 30, 25.00, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(2, 2, 'Hair Coloring', 'Full hair coloring service', 'male', 2, 90, 50.00, 1, '2025-06-02 02:10:56', '2025-07-09 03:23:23', NULL),
(3, 2, 'Styling', 'Hair styling for special occasions', 'male', 0, 45, 20.00, 1, '2025-06-02 02:10:56', '2025-07-09 03:23:29', NULL),
(4, 1, 'Facial', 'Basic facial treatment', 'male', 0, 60, 50.00, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(5, 1, 'Manicure', 'Basic manicure service', 'male', 0, 30, 20.00, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL),
(7, 2, 'malish', 'test', 'male', 0, 30, 75.00, 1, '2025-06-03 03:25:50', '2025-06-03 03:25:50', NULL),
(9, 2, 'Wax', 'Ss', 'female', 0, 30, 59.00, 1, '2025-06-19 19:26:18', '2025-07-04 01:22:30', NULL),
(10, 7, 'Hair Cutting', 'Hair Cutting', 'male', 0, 30, 250.00, 1, '2025-06-21 09:42:21', '2025-06-21 09:42:21', NULL),
(11, 7, 'Eyebrow', 'Eyebrow', 'male', 0, 15, 100.00, 1, '2025-06-21 09:42:53', '2025-06-21 09:42:53', NULL),
(12, 7, 'Hair Color', 'Black, Red, Brown', 'male', 0, 60, 450.00, 1, '2025-06-21 09:43:46', '2025-06-21 09:43:46', NULL),
(13, 7, 'Makeup (HD)', 'Brand Makeup', 'male', 0, 30, 2500.00, 1, '2025-06-21 09:46:43', '2025-06-21 09:46:43', NULL),
(14, 4, 'Hair color', 'test', 'male', 0, 30, 123.00, 1, '2025-06-24 02:53:41', '2025-06-24 02:53:41', NULL),
(16, 3, 'Hair color', NULL, 'male', 0, 30, 45.00, 1, '2025-06-25 08:26:23', '2025-06-25 08:26:23', NULL),
(17, 8, 'hair', NULL, 'male', 0, 30, 1.00, 1, '2025-06-27 05:21:25', '2025-06-27 05:21:25', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `subscription_plans`
--

CREATE TABLE `subscription_plans` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `price` decimal(10,2) NOT NULL,
  `currency_symbol` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '₹',
  `currency_code` varchar(8) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'INR',
  `billing_cycle` enum('monthly','yearly') COLLATE utf8mb4_unicode_ci NOT NULL,
  `max_services` int NOT NULL DEFAULT '0',
  `max_appointments_per_month` int NOT NULL DEFAULT '0',
  `max_seats` int NOT NULL DEFAULT '0',
  `max_branches` int NOT NULL DEFAULT '1',
  `max_staff` int NOT NULL DEFAULT '0',
  `has_analytics` tinyint(1) NOT NULL DEFAULT '0',
  `has_api_access` tinyint(1) NOT NULL DEFAULT '0',
  `has_custom_branding` tinyint(1) NOT NULL DEFAULT '0',
  `has_priority_support` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscription_plans`
--

INSERT INTO `subscription_plans` (`id`, `name`, `description`, `price`, `currency_symbol`, `currency_code`, `billing_cycle`, `max_services`, `max_appointments_per_month`, `max_seats`, `max_branches`, `max_staff`, `has_analytics`, `has_api_access`, `has_custom_branding`, `has_priority_support`, `is_active`, `sort_order`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Starter', 'Perfect for small salons just getting started', 2499.00, '₹', 'INR', 'monthly', 10, 100, 3, 1, 2, 0, 0, 0, 0, 1, 6, '2025-06-02 02:11:01', '2025-07-14 01:33:24', NULL),
(2, 'Professional', 'Ideal for growing salons with multiple staff', 4999.00, '₹', 'INR', 'monthly', 25, 500, 8, 2, 10, 1, 0, 0, 0, 1, 2, '2025-06-02 02:11:01', '2025-07-14 01:29:03', NULL),
(3, 'Business', 'For established salons with multiple locations', 8499.00, '₹', 'INR', 'monthly', 50, 1000, 15, 5, 25, 1, 1, 1, 0, 1, 3, '2025-06-02 02:11:01', '2025-06-02 02:11:01', NULL),
(4, 'Enterprise', 'Unlimited everything for large salon chains', 16999.00, '₹', 'INR', 'monthly', 0, 0, 0, 10, 0, 1, 1, 1, 1, 1, 4, '2025-06-02 02:11:01', '2025-07-15 00:08:02', NULL),
(5, 'Starter (Yearly)', 'Perfect for small salons - Save 20% with yearly billing', 23990.00, '₹', 'INR', 'yearly', 10, 100, 3, 1, 2, 0, 0, 0, 0, 1, 5, '2025-06-02 02:11:01', '2025-07-15 00:07:03', NULL),
(6, 'Professional (Yearly)', 'Ideal for growing salons - Save 20% with yearly billing', 1.00, '₹', 'INR', 'yearly', 50, 500, 8, 50, 50, 1, 0, 0, 0, 1, 1, '2025-06-02 02:11:01', '2025-07-15 01:04:08', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `tenants`
--

CREATE TABLE `tenants` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin
) ;

--
-- Dumping data for table `tenants`
--

INSERT INTO `tenants` (`id`, `created_at`, `updated_at`, `data`) VALUES
('bonanza', '2025-06-02 02:17:20', '2025-06-02 02:17:20', '[]'),
('dipesh', '2025-06-19 05:23:13', '2025-06-19 05:23:13', '[]'),
('Keshvi', '2025-06-21 09:28:10', '2025-06-21 09:28:10', '[]'),
('kevin', '2025-06-19 05:30:50', '2025-06-19 05:30:50', '[]'),
('sahil', '2025-06-13 10:52:04', '2025-06-13 10:52:04', '[]');

-- --------------------------------------------------------

--
-- Table structure for table `terms_conditions`
--

CREATE TABLE `terms_conditions` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `condition` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `terms_conditions`
--

INSERT INTO `terms_conditions` (`id`, `user_id`, `branch_id`, `title`, `condition`, `is_default`, `is_active`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 2, 'Standard Terms', 'These are the standard terms and conditions for our service.', 1, 1, '2025-06-02 02:10:56', '2025-06-02 02:10:56', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_user_id` bigint UNSIGNED DEFAULT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_domain` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tenant_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `branch_id` bigint UNSIGNED DEFAULT NULL,
  `current_branch_id` bigint UNSIGNED DEFAULT NULL,
  `customer_branch_ids` longtext COLLATE utf8mb4_unicode_ci,
  `logo` text COLLATE utf8mb4_unicode_ci,
  `profile` text COLLATE utf8mb4_unicode_ci,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` enum('male','female','other') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `anniversary` date DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `parent_user_id`, `company_name`, `company_domain`, `tenant_id`, `branch_id`, `current_branch_id`, `customer_branch_ids`, `logo`, `profile`, `email`, `phone`, `gender`, `anniversary`, `date_of_birth`, `address`, `is_active`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'Admin User', NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, '<EMAIL>', '1234567890', 'male', NULL, NULL, NULL, 1, '2025-06-02 02:10:55', '$2y$10$jjFWFFplP0qmwy9wTw0XF.lXJLd6u6l7KeCmv2hKlaJfWlkoZXsn.', 'S3pXyKIfpahJmUMshtfPxOBWcZXxdl34YYTqYGEjaDAjPvmiOFGcj3JOPnp1', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(2, 'John Stylist', NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, '<EMAIL>', '1111222233', 'male', NULL, NULL, NULL, 1, '2025-06-02 02:10:56', '$2y$10$jjFWFFplP0qmwy9wTw0XF.lXJLd6u6l7KeCmv2hKlaJfWlkoZXsn.', 'SzBBd16Fk5', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(3, 'Jane Beautician', NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, '<EMAIL>', '4444555566', 'female', NULL, NULL, NULL, 1, '2025-06-02 02:10:56', '$2y$10$TUJJEQzR7miTTrh2v9ABfeCZqvXO1InE99kculAufs2ylusQI5yUW', 'eu5MYkDIO5', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(4, 'Mike Barber', NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, '<EMAIL>', '7777888899', 'male', NULL, NULL, NULL, 1, '2025-06-02 02:10:56', '$2y$10$EI9hiK2LIPgC/OgYwRLBm.Qxw.TFYuKl/GN9hHB91Ij01XX374tmi', '0br1wzUJQ7', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(5, 'Customer One', NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, '<EMAIL>', '1212121212', 'male', NULL, NULL, NULL, 1, '2025-06-02 02:10:56', '$2y$10$u1Rfbq8g64rgqcffRZTN9.xRr5IRoWq8xCPc0FCCdY7PVcHk09A5i', 'qHawcrqyv0d4NGjfAA6wnrchsWLCsF88V9gCw75QsHxStD8tvIx5kV3UeLtW', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(6, 'Customer Two', NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, '<EMAIL>', '2323232323', 'female', NULL, NULL, NULL, 1, '2025-06-02 02:10:56', '$2y$10$dMXkQxA9.bwWUgL.84O6NeKk/9y2RDIpXJ9eTxpr5E2ZeUpqR0pXy', 'jhuv8fsaVQ', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(7, 'Customer Three', NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, '<EMAIL>', '3434343434', 'other', NULL, NULL, NULL, 1, '2025-06-02 02:10:56', '$2y$10$9TbpfcZemYHt5iXP6MAMq.EfaOBXTYGdRkhyUSEeuEA5tKgzm4uyC', '49UVoWM0eH', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(8, 'Bonanza', NULL, 'Bonanza', 'bonanza', 'bonanza', 2, 2, NULL, 'profile-logos/cW0HWkP7QjNA5pDyITCm71pbigNxdbAWxPHozXcd.jpg', 'profile-images/JCkT9Z4kdXehUHnQWkR0LvAJPY2ZH7l4pXLibXeI.jpg', '<EMAIL>', '********', 'male', NULL, NULL, '122210 Abc Av, Norwalk', 1, NULL, '$2y$10$u1Rfbq8g64rgqcffRZTN9.xRr5IRoWq8xCPc0FCCdY7PVcHk09A5i', NULL, '2025-06-02 02:17:21', '2025-07-09 04:28:00'),
(10, 'Rohit staff', NULL, NULL, NULL, NULL, 2, 2, NULL, NULL, NULL, '<EMAIL>', '4444555566', 'female', NULL, NULL, NULL, 1, '2025-06-02 02:10:56', '$2y$10$TUJJEQzR7miTTrh2v9ABfeCZqvXO1InE99kculAufs2ylusQI5yUW', 'eu5MYkDIO5', '2025-06-02 02:10:56', '2025-06-02 02:10:56'),
(11, 'kishan', NULL, NULL, NULL, 'bonanza', 2, 2, '2', NULL, 'profile-images/bGctHwacGe5H8MRNcLfUbFg0cPyCWrBP4AKhtZ0F.jpg', '<EMAIL>', '**********', 'female', '2025-07-01', '2025-07-10', 'kishan kishan', 1, NULL, '$2y$10$YBnDP8JSTwOO/BcB/FR4f.bzxjEyPKSR69O9GHApPAchqCHVwCwtu', NULL, '2025-06-02 05:05:06', '2025-07-07 03:23:05'),
(13, 'Sahil', NULL, 'Sahil', 'sahil', 'sahil', NULL, 8, NULL, 'profile-logos/t1Yk3KrIIWowI0W2DXl9TPsRv00dM9jBeTDLMwyA.jpg', NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$zvo.MJVWGYl.n2elzjnhHuH2yNeDyr7XCEOAcipSMt2axVaVUe7/q', NULL, '2025-06-13 10:52:04', '2025-06-27 06:45:58'),
(14, 'dipesh', NULL, 'Dipesh', 'dipesh', 'dipesh', NULL, 5, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$QYevcPgTJuSMAVyONLyXBuXkhN0HjpS7Nxs51G6IcMb44G8RfmmAu', NULL, '2025-06-19 05:23:13', '2025-06-19 05:23:13'),
(15, 'kevin', NULL, 'Kevin Salon', 'kevin', 'kevin', NULL, 6, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$XmO1JR1J.ZoEWS7bBpQ1Q.Tz5bxtv8W51KQOBtrtcDeVdlmSvIMbm', NULL, '2025-06-19 05:30:50', '2025-06-19 05:30:50'),
(16, 'customer', NULL, NULL, NULL, 'bonanza', NULL, NULL, NULL, NULL, NULL, '<EMAIL>', '**********', NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$q7DwItFXnzYgFquUXF5kTubFg0Oy0buK/SBp6BC/BWXm6BzNGeN7u', NULL, '2025-06-19 05:42:26', '2025-06-19 05:42:26'),
(17, 'test', NULL, NULL, NULL, 'bonanza', NULL, NULL, NULL, NULL, NULL, '<EMAIL>', '88888888878', NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$IBIe.Zy9OJ5Kl9g643M62.n1iaSDX0.tklXGKcShSLunkuO9ttc8C', NULL, '2025-06-19 05:44:27', '2025-06-19 05:44:27'),
(18, 'Loshh', NULL, NULL, NULL, 'bonanza', NULL, NULL, NULL, NULL, NULL, '<EMAIL>', '8585888288', NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$DfpG9xEbzv5AgK.SRD.dqObvKHMZVFd4jrG6qRoofTgx79O2uW74m', NULL, '2025-06-19 19:12:17', '2025-06-19 19:12:17'),
(19, 'Prince Akabari', NULL, 'Keshvi Bridal Makeover', 'Keshvi', 'Keshvi', NULL, 7, NULL, NULL, NULL, '<EMAIL>', '+91 123 456 7890', 'male', NULL, NULL, NULL, 1, NULL, '$2y$10$v51nY2ObntZLioh8Xm6AzelM4fNE0KWzLOQSk5QWoLANGzCQKjbBS', NULL, '2025-06-21 09:28:10', '2025-06-21 09:40:53'),
(20, 'Zeph Wiley', NULL, NULL, NULL, NULL, 7, 7, NULL, NULL, NULL, '<EMAIL>', '+1 (542) 287-9622', 'female', NULL, NULL, 'Reiciendis quo venia', 1, NULL, '$2y$10$XfTJSmz7IvJ0z6GrjIx81.eO5LrF0q2hoQE6AIMbyGXGATtyRfCvq', NULL, '2025-06-21 09:51:52', '2025-06-21 09:51:52'),
(21, 'esha', NULL, NULL, NULL, 'Keshvi', NULL, NULL, NULL, NULL, NULL, '<EMAIL>', '8488892028', NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$kU95IxXywfje2erkrOCXLuPbCDNU/eYUamjb2hFjsM/QTabbGqXxW', NULL, '2025-06-21 10:07:09', '2025-06-21 10:07:09'),
(22, 'mohan', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$qRiDm6DDKkSs70ycVL3xouCYQumBWBbU7JjYp1JU7tLLfq4JpMsRW', NULL, '2025-06-24 04:53:01', '2025-06-24 04:53:01'),
(23, 'chhagan', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$ksqmBFEqpiN/8zH37kacReqL7vEBzQWePb5BXeUI4aUhYsUX4CNFi', NULL, '2025-06-24 04:54:35', '2025-06-24 04:54:35'),
(24, 'Manager', NULL, NULL, NULL, 'bonanza', 2, 2, '2,3', NULL, NULL, '<EMAIL>', '**********', 'male', NULL, NULL, '122210 Abc Av, Norwalk', 1, NULL, '$2y$10$SK.gvf0bkHlBKpFsvEDH2.HMA28VlFxGk5HJKXUwNENzhApfcD/MO', NULL, '2025-06-25 01:42:31', '2025-06-25 01:46:33'),
(25, 'blabla', NULL, NULL, NULL, NULL, NULL, NULL, '2', NULL, NULL, '<EMAIL>', '**********', 'female', NULL, NULL, '122210 Abc Av, Norwalk', 1, NULL, '$2y$10$J.xXHprExBJkh4pK5q0WyOXTYbYnUGaUdr7bVEvUOSEkDTJpC8zWa', NULL, '2025-06-25 01:57:23', '2025-06-25 01:57:23'),
(26, 'vimal', NULL, NULL, NULL, NULL, NULL, NULL, '2', NULL, NULL, '<EMAIL>', '**********', 'male', NULL, NULL, '122210 Abc Av, Norwalk', 1, NULL, '$2y$10$r9C/WEwGlhjtXtUhVBvkM.huUJK8.adm2EbifcRUge8EZ95qor7wG', NULL, '2025-06-25 02:14:21', '2025-06-25 02:14:21'),
(27, 'nilu', NULL, NULL, NULL, NULL, NULL, NULL, '2', NULL, NULL, '<EMAIL>', '**********', 'female', '1999-01-08', '1982-01-01', '122210 Abc Av, Norwalk', 1, NULL, '$2y$10$RZq8/keklraVeFho8vYz2uAnHsmrP5BcLGr5lQyXVvAB4eURD9eJm', NULL, '2025-06-25 02:26:13', '2025-07-07 00:57:04'),
(28, 'trivedi', NULL, NULL, NULL, 'bonanza', NULL, NULL, NULL, NULL, NULL, '<EMAIL>', '**********', NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$xWI14p.H/Us/OfW8mWArW.4K5EQrUXuE20KzoWElVSaoOCIB4kRES', NULL, '2025-06-26 02:04:41', '2025-06-26 02:04:41'),
(29, 'sahil nu salon staff', NULL, NULL, NULL, NULL, 4, 4, NULL, NULL, NULL, '<EMAIL>', '**********', 'male', NULL, NULL, 'test', 1, NULL, '$2y$10$dOWjwSTC/klNzJ3yBa/WV.J1spy.qxMijiPFqnvUm1fX/VMLzCOsW', NULL, '2025-06-27 04:50:48', '2025-06-27 04:50:48'),
(30, 'venus', NULL, NULL, NULL, NULL, 8, 8, NULL, NULL, NULL, '<EMAIL>', '**********', 'male', NULL, NULL, 'tester', 1, NULL, '$2y$10$x2B0ef4GpAFJ8n0F3NvynuwM9.AIZROJwmPyTpkVxpkckKuErQNb6', NULL, '2025-06-27 05:21:57', '2025-06-27 05:21:57'),
(35, 'Bonanza', 8, 'Bonanza', NULL, 'bonanza', 2, 2, NULL, NULL, NULL, '<EMAIL>', '**********', 'male', NULL, NULL, '122210 Abc Av, Norwalk', 1, NULL, '$2y$10$NCn3CBGz8XqKcvBf3bqVQubFj0oc3M16.RQcWOyMDUhoNRfZqdMDe', NULL, '2025-07-04 02:55:45', '2025-07-04 03:43:55'),
(36, 'Glam Haven', 8, 'Bonanza', NULL, 'bonanza', 3, 3, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$pmDxTxuDu1QFdIdGwUowquJ4V3UwxrW56AEgHoioqf2ddg4tfl5JG', NULL, '2025-07-04 02:55:48', '2025-07-04 03:26:32'),
(37, 'krunal', 8, 'Bonanza', NULL, 'bonanza', 10, 10, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, NULL, NULL, 1, NULL, '$2y$10$kcv5Udy1.ESp1KxMRji7KOzThamDcC2yL0fLchKVEh2zEJd5N0ZTe', NULL, '2025-07-04 03:27:33', '2025-07-04 03:30:37'),
(38, 'sahil', NULL, NULL, NULL, 'Keshvi', NULL, NULL, NULL, NULL, NULL, '<EMAIL>', '8555554455', NULL, '2017-01-07', '1997-01-07', NULL, 1, NULL, '$2y$10$HhXoRwvbbaURFeNBG0.ume4HKc5WiwJIl/Zq3Pz4UERrN74Bkr6ya', NULL, '2025-07-07 01:08:47', '2025-07-07 01:08:47'),
(39, 'mohan', 8, 'Bonanza', NULL, 'bonanza', 2, 2, NULL, NULL, NULL, '<EMAIL>', '**********', 'male', NULL, NULL, 'tester', 1, NULL, '$2y$10$vxQvTsjVyTdQPp7GcHuUGOEKGObMOlPUOz1RYcIT6MCtG8aQRyNo2', NULL, '2025-07-07 03:11:08', '2025-07-08 04:52:07'),
(40, 'kishan', 8, 'Bonanza', NULL, 'bonanza', 3, 3, NULL, NULL, NULL, '<EMAIL>', '**********', 'male', NULL, NULL, 'test', 1, NULL, '$2y$10$V849jwFAYC98ReNzKYaFguqbh3ANF9RqmyZ7FbychJdRYC86W5uly', NULL, '2025-07-15 05:08:21', '2025-07-15 05:08:21');

-- --------------------------------------------------------

--
-- Table structure for table `vendor_customers`
--

CREATE TABLE `vendor_customers` (
  `id` bigint UNSIGNED NOT NULL,
  `vendor_id` bigint UNSIGNED NOT NULL,
  `customer_ids` text COLLATE utf8mb4_unicode_ci COMMENT 'Comma separated customer IDs',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `vendor_subscriptions`
--

CREATE TABLE `vendor_subscriptions` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `subscription_plan_id` bigint UNSIGNED NOT NULL,
  `plan_transaction_id` bigint UNSIGNED DEFAULT NULL,
  `starts_at` datetime NOT NULL,
  `ends_at` datetime NOT NULL,
  `trial_ends_at` datetime DEFAULT NULL,
  `status` enum('active','inactive','cancelled','expired','trial') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'trial',
  `current_services_count` int NOT NULL DEFAULT '0',
  `current_appointments_count` int NOT NULL DEFAULT '0',
  `current_seats_count` int NOT NULL DEFAULT '0',
  `current_branches_count` int NOT NULL DEFAULT '0',
  `current_staff_count` int NOT NULL DEFAULT '0',
  `billing_period_start` date DEFAULT NULL,
  `billing_period_end` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `vendor_subscriptions`
--

INSERT INTO `vendor_subscriptions` (`id`, `user_id`, `subscription_plan_id`, `plan_transaction_id`, `starts_at`, `ends_at`, `trial_ends_at`, `status`, `current_services_count`, `current_appointments_count`, `current_seats_count`, `current_branches_count`, `current_staff_count`, `billing_period_start`, `billing_period_end`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 8, 1, NULL, '2025-06-30 23:59:59', '2025-06-16 07:47:21', '2025-06-16 07:47:21', 'trial', 0, 0, 0, 1, 0, '2025-06-01', '2025-06-30', '2025-06-02 02:17:21', '2025-07-15 00:16:43', NULL),
(2, 13, 1, NULL, '2025-06-30 23:59:59', '2025-06-27 10:52:04', '2025-06-27 10:52:04', 'trial', 0, 0, 0, 1, 0, '2025-06-01', '2025-06-30', '2025-06-13 10:52:04', '2025-06-13 10:52:04', NULL),
(3, 14, 1, NULL, '2025-06-30 23:59:59', '2025-07-03 05:23:13', '2025-07-03 05:23:13', 'trial', 0, 0, 0, 1, 0, '2025-06-01', '2025-06-30', '2025-06-19 05:23:13', '2025-06-19 05:23:13', NULL),
(4, 15, 1, NULL, '2025-06-30 23:59:59', '2025-10-17 00:00:00', '2025-11-18 00:00:00', 'trial', 0, 0, 0, 1, 0, '2025-06-01', '2025-06-30', '2025-06-19 05:30:50', '2025-07-14 02:37:45', NULL),
(5, 19, 1, NULL, '2025-06-30 23:59:59', '2025-07-05 09:28:10', '2025-07-05 09:28:10', 'active', 0, 0, 0, 1, 0, '2025-06-01', '2025-06-30', '2025-06-21 09:28:10', '2025-07-14 01:52:50', NULL),
(14, 8, 6, 3, '2025-07-15 08:25:14', '2026-07-15 08:25:14', NULL, 'active', 5, 14, 4, 3, 2, '2025-07-01', '2025-07-31', '2025-07-15 02:55:14', '2025-07-15 02:55:14', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `working_hours`
--

CREATE TABLE `working_hours` (
  `id` bigint UNSIGNED NOT NULL,
  `branch_id` bigint UNSIGNED NOT NULL,
  `working_hours` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `working_hours`
--

INSERT INTO `working_hours` (`id`, `branch_id`, `working_hours`, `created_at`, `updated_at`) VALUES
(2, 8, '[{\"day\": \"Monday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Tuesday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Wednesday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Thursday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Friday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Saturday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Sunday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}]', '2025-06-27 08:09:02', '2025-06-27 08:14:40'),
(3, 2, '[{\"day\": \"Monday\", \"open\": \"09:00\", \"close\": \"19:00\", \"is_closed\": true}, {\"day\": \"Tuesday\", \"open\": \"09:00\", \"close\": \"19:00\", \"is_closed\": false}, {\"day\": \"Wednesday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Thursday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Friday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Saturday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Sunday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}]', '2025-07-03 23:51:45', '2025-07-08 02:52:01'),
(5, 3, '[{\"day\": \"Monday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Tuesday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Wednesday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Thursday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Friday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Saturday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Sunday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}]', '2025-07-08 03:05:41', '2025-07-08 03:05:41'),
(6, 10, '[{\"day\": \"Monday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}, {\"day\": \"Tuesday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}, {\"day\": \"Wednesday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}, {\"day\": \"Thursday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}, {\"day\": \"Friday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}, {\"day\": \"Saturday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": false}, {\"day\": \"Sunday\", \"open\": \"09:00\", \"close\": \"18:00\", \"is_closed\": true}]', '2025-07-08 03:06:11', '2025-07-08 03:06:17');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `appointments`
--
ALTER TABLE `appointments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `appointments_user_id_foreign` (`user_id`),
  ADD KEY `appointments_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `appointment_services`
--
ALTER TABLE `appointment_services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `appointment_services_appointment_id_foreign` (`appointment_id`),
  ADD KEY `appointment_services_service_id_foreign` (`service_id`),
  ADD KEY `appointment_services_seat_id_foreign` (`seat_id`),
  ADD KEY `appointment_services_plan_used_service_foreign` (`plan_used_service`);

--
-- Indexes for table `branches`
--
ALTER TABLE `branches`
  ADD PRIMARY KEY (`id`),
  ADD KEY `branches_user_id_foreign` (`user_id`),
  ADD KEY `branches_branch_user_id_foreign` (`branch_user_id`);

--
-- Indexes for table `branch_media`
--
ALTER TABLE `branch_media`
  ADD PRIMARY KEY (`id`),
  ADD KEY `branch_media_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `domains`
--
ALTER TABLE `domains`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `domains_domain_unique` (`domain`),
  ADD KEY `domains_tenant_id_foreign` (`tenant_id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `plans`
--
ALTER TABLE `plans`
  ADD PRIMARY KEY (`id`),
  ADD KEY `plans_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `plan_services`
--
ALTER TABLE `plan_services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `plan_services_plan_id_foreign` (`plan_id`),
  ADD KEY `plan_services_service_id_foreign` (`service_id`);

--
-- Indexes for table `plan_service_usages`
--
ALTER TABLE `plan_service_usages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `plan_service_usages_plan_usage_id_foreign` (`plan_usage_id`),
  ADD KEY `plan_service_usages_service_id_foreign` (`service_id`),
  ADD KEY `plan_service_usages_appointment_id_foreign` (`appointment_id`);

--
-- Indexes for table `plan_transactions`
--
ALTER TABLE `plan_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `plan_transactions_razorpay_order_id_unique` (`razorpay_order_id`),
  ADD KEY `plan_transactions_payment_method_id_foreign` (`payment_method_id`),
  ADD KEY `plan_transactions_user_id_status_index` (`user_id`,`status`),
  ADD KEY `plan_transactions_subscription_plan_id_status_index` (`subscription_plan_id`,`status`),
  ADD KEY `plan_transactions_razorpay_order_id_index` (`razorpay_order_id`),
  ADD KEY `plan_transactions_razorpay_payment_id_index` (`razorpay_payment_id`);

--
-- Indexes for table `plan_usages`
--
ALTER TABLE `plan_usages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `plan_usages_user_id_foreign` (`user_id`),
  ADD KEY `plan_usages_plan_id_foreign` (`plan_id`),
  ADD KEY `plan_usages_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `seats`
--
ALTER TABLE `seats`
  ADD PRIMARY KEY (`id`),
  ADD KEY `seats_branch_id_foreign` (`branch_id`),
  ADD KEY `seats_staff_id_foreign` (`staff_id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `services_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tenants`
--
ALTER TABLE `tenants`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `terms_conditions`
--
ALTER TABLE `terms_conditions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `terms_conditions_user_id_foreign` (`user_id`),
  ADD KEY `terms_conditions_branch_id_foreign` (`branch_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD UNIQUE KEY `users_company_domain_unique` (`company_domain`),
  ADD KEY `users_parent_user_id_foreign` (`parent_user_id`);

--
-- Indexes for table `vendor_customers`
--
ALTER TABLE `vendor_customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `vendor_customers_vendor_id_foreign` (`vendor_id`);

--
-- Indexes for table `vendor_subscriptions`
--
ALTER TABLE `vendor_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `vendor_subscriptions_subscription_plan_id_foreign` (`subscription_plan_id`),
  ADD KEY `vendor_subscriptions_plan_transaction_id_index` (`plan_transaction_id`),
  ADD KEY `vendor_subscriptions_user_id_foreign` (`user_id`);

--
-- Indexes for table `working_hours`
--
ALTER TABLE `working_hours`
  ADD PRIMARY KEY (`id`),
  ADD KEY `working_hours_branch_id_foreign` (`branch_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `appointments`
--
ALTER TABLE `appointments`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

--
-- AUTO_INCREMENT for table `appointment_services`
--
ALTER TABLE `appointment_services`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=86;

--
-- AUTO_INCREMENT for table `branches`
--
ALTER TABLE `branches`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `branch_media`
--
ALTER TABLE `branch_media`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `domains`
--
ALTER TABLE `domains`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `plans`
--
ALTER TABLE `plans`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `plan_services`
--
ALTER TABLE `plan_services`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `plan_service_usages`
--
ALTER TABLE `plan_service_usages`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `plan_transactions`
--
ALTER TABLE `plan_transactions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `plan_usages`
--
ALTER TABLE `plan_usages`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `seats`
--
ALTER TABLE `seats`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `subscription_plans`
--
ALTER TABLE `subscription_plans`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `terms_conditions`
--
ALTER TABLE `terms_conditions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=41;

--
-- AUTO_INCREMENT for table `vendor_customers`
--
ALTER TABLE `vendor_customers`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `vendor_subscriptions`
--
ALTER TABLE `vendor_subscriptions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `working_hours`
--
ALTER TABLE `working_hours`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `appointments`
--
ALTER TABLE `appointments`
  ADD CONSTRAINT `appointments_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `appointments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `appointment_services`
--
ALTER TABLE `appointment_services`
  ADD CONSTRAINT `appointment_services_appointment_id_foreign` FOREIGN KEY (`appointment_id`) REFERENCES `appointments` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `appointment_services_plan_used_service_foreign` FOREIGN KEY (`plan_used_service`) REFERENCES `plan_service_usages` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `appointment_services_seat_id_foreign` FOREIGN KEY (`seat_id`) REFERENCES `seats` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `appointment_services_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `branches`
--
ALTER TABLE `branches`
  ADD CONSTRAINT `branches_branch_user_id_foreign` FOREIGN KEY (`branch_user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `branches_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `branch_media`
--
ALTER TABLE `branch_media`
  ADD CONSTRAINT `branch_media_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `domains`
--
ALTER TABLE `domains`
  ADD CONSTRAINT `domains_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plans`
--
ALTER TABLE `plans`
  ADD CONSTRAINT `plans_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plan_services`
--
ALTER TABLE `plan_services`
  ADD CONSTRAINT `plan_services_plan_id_foreign` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `plan_services_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plan_service_usages`
--
ALTER TABLE `plan_service_usages`
  ADD CONSTRAINT `plan_service_usages_appointment_id_foreign` FOREIGN KEY (`appointment_id`) REFERENCES `appointments` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `plan_service_usages_plan_usage_id_foreign` FOREIGN KEY (`plan_usage_id`) REFERENCES `plan_usages` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `plan_service_usages_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plan_transactions`
--
ALTER TABLE `plan_transactions`
  ADD CONSTRAINT `plan_transactions_payment_method_id_foreign` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `plan_transactions_subscription_plan_id_foreign` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `plan_transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `plan_usages`
--
ALTER TABLE `plan_usages`
  ADD CONSTRAINT `plan_usages_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `plan_usages_plan_id_foreign` FOREIGN KEY (`plan_id`) REFERENCES `plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `plan_usages_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `seats`
--
ALTER TABLE `seats`
  ADD CONSTRAINT `seats_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `seats_staff_id_foreign` FOREIGN KEY (`staff_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `services`
--
ALTER TABLE `services`
  ADD CONSTRAINT `services_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`);

--
-- Constraints for table `terms_conditions`
--
ALTER TABLE `terms_conditions`
  ADD CONSTRAINT `terms_conditions_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`),
  ADD CONSTRAINT `terms_conditions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_parent_user_id_foreign` FOREIGN KEY (`parent_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `vendor_customers`
--
ALTER TABLE `vendor_customers`
  ADD CONSTRAINT `vendor_customers_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `vendor_subscriptions`
--
ALTER TABLE `vendor_subscriptions`
  ADD CONSTRAINT `vendor_subscriptions_plan_transaction_id_foreign` FOREIGN KEY (`plan_transaction_id`) REFERENCES `plan_transactions` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `vendor_subscriptions_subscription_plan_id_foreign` FOREIGN KEY (`subscription_plan_id`) REFERENCES `subscription_plans` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `vendor_subscriptions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `working_hours`
--
ALTER TABLE `working_hours`
  ADD CONSTRAINT `working_hours_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
