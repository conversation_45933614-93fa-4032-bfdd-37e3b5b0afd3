import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '../../store/themeConfigSlice';
import { router } from '@inertiajs/react';
import Swal from 'sweetalert2';

interface SubscriptionPlan {
  id: number;
  name: string;
  description: string;
  price: number;
  currency_symbol: string;
  currency_code: string;
  billing_cycle: string;
  max_services: number;
  max_appointments_per_month: number;
  max_seats: number;
  max_branches: number;
  max_staff: number;
  has_analytics: boolean;
  has_api_access: boolean;
  has_custom_branding: boolean;
  has_priority_support: boolean;
}

interface PaymentMethod {
  id: number;
  name: string;
  type: string;
  mode: string;
  details: {
    key_id: string;
    key_secret: string;
  };
}

interface AuthUser {
  id: number;
  name: string;
  email: string;
  phone: string;
}

interface ActiveSubscription {
  id: number;
  plan_name: string;
  ends_at: string;
  days_remaining: number;
}

interface Props {
  plan: SubscriptionPlan;
  paymentMethods: PaymentMethod[];
  authUser: AuthUser | null;
  hasActiveSubscription: boolean;
  activeSubscription: ActiveSubscription | null;
}

const SubscriptionCheckout: React.FC<Props> = ({ plan, paymentMethods, authUser, hasActiveSubscription, activeSubscription }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [razorpayLoaded, setRazorpayLoaded] = useState(false);

  useEffect(() => {
    dispatch(setPageTitle(`Checkout - ${plan.name}`));
  }, [dispatch, plan.name]);

  // Load Razorpay script
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    script.onload = () => setRazorpayLoaded(true);
    script.onerror = () => {
      console.error('Failed to load Razorpay script');
      Swal.fire({
        icon: 'error',
        title: 'Payment system unavailable',
        text: 'Please try again later.',
      });
    };
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  // Redirect to login if no authenticated user (shouldn't happen due to backend check)
  useEffect(() => {
    if (!authUser) {
      window.location.href = '/auth/login';
    }
  }, [authUser]);

  const handlePayment = async () => {
    // Ensure user is authenticated
    if (!authUser) {
      window.location.href = '/auth/login';
      return;
    }

    // Check if user already has an active subscription
    if (hasActiveSubscription) {
      Swal.fire({
        icon: 'warning',
        title: 'Active Subscription Found',
        html: `You already have an active subscription (<strong>${activeSubscription?.plan_name}</strong>) that expires on ${activeSubscription?.ends_at}.<br><br>If you want to deactivate your current plan and purchase a new one, please contact the administrator at <a href="mailto:<EMAIL>" class="text-blue-600 underline"><EMAIL></a>.`,
        confirmButtonText: 'Understood',
      });
      return;
    }

    // Check if Razorpay is loaded
    if (!razorpayLoaded || !window.Razorpay) {
      Swal.fire({
        icon: 'error',
        title: 'Payment system loading...',
        text: 'Please wait a moment and try again.',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
      });
      return;
    }

    const razorpayMethod = paymentMethods.find(method => method.type === 'razorpay');
    if (!razorpayMethod) {
      Swal.fire({
        icon: 'error',
        title: 'Payment method not available',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
      });
      return;
    }

    setLoading(true);

    try {
      // Create Razorpay order - using web route instead of API
      const orderResponse = await fetch('/checkout/subscription/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify({
          amount: plan.price * 100, // Convert to paise
          currency: plan.currency_code,
          plan_id: plan.id,
        }),
      });

      const orderData = await orderResponse.json();

      if (!orderData.success) {
        throw new Error(orderData.message || 'Failed to create order');
      }

      // Store transaction ID for later use
      const transactionId = orderData.transaction_id;

      // Initialize Razorpay
      const options = {
        key: razorpayMethod.details.key_id,
        amount: orderData.order.amount,
        currency: orderData.order.currency,
        name: 'Salozy',
        description: `${plan.name} Subscription`,
        order_id: orderData.order.id,
        handler: function (response: any) {
          // Process payment
          router.post('/checkout/subscription/process', {
            plan_id: plan.id,
            payment_method_id: razorpayMethod.id,
            payment_id: response.razorpay_payment_id,
            order_id: response.razorpay_order_id,
            signature: response.razorpay_signature,
            transaction_id: transactionId, // Pass the transaction ID
          }, {
            onError: (errors) => {
              setLoading(false);
              // Handle payment processing errors
              if (transactionId) {
                fetch('/checkout/subscription/update-status', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                  },
                  body: JSON.stringify({
                    transaction_id: transactionId,
                    status: 'failed',
                    reason: errors.payment || 'Payment processing failed',
                  }),
                }).then(response => response.json()).then(data => {
                  // Redirect to main home page after updating transaction status
                  setTimeout(() => {
                    window.location.href = data.redirect_url || '/';
                  }, 3000); // Wait 3 seconds to show the error message
                }).catch(console.error);
              } else {
                // If no transaction ID, redirect immediately after showing error
                setTimeout(() => {
                  window.location.href = '/';
                }, 3000);
              }

              Swal.fire({
                icon: 'error',
                title: 'Payment Failed',
                text: errors.payment || 'Payment processing failed. You will be redirected to the dashboard.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 5000,
              });
            },
            onSuccess: () => {
              setLoading(false);
              Swal.fire({
                icon: 'success',
                title: 'Payment Successful!',
                text: 'Your subscription has been activated successfully. Redirecting to home page...',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
              });
              // Note: The backend already redirects to vendor-home on successful payment
              // This is just for additional feedback to the user
            }
          });
        },
        prefill: {
          name: authUser.name,
          email: authUser.email,
          contact: authUser.phone,
        },
        theme: {
          color: '#3B82F6',
        },
        modal: {
          ondismiss: function () {
            setLoading(false);
            // Handle payment cancellation - update transaction status using web route
            if (transactionId) {
              fetch('/checkout/subscription/update-status', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                  transaction_id: transactionId,
                  status: 'cancelled',
                  reason: 'User cancelled the payment',
                }),
              }).then(response => response.json()).then(data => {
                // Show cancellation message
                Swal.fire({
                  icon: 'info',
                  title: 'Payment Cancelled',
                  text: 'You have cancelled the payment process. Redirecting to home page...',
                  toast: true,
                  position: 'top-end',
                  showConfirmButton: false,
                  timer: 3000,
                });

                // Redirect to main home page after updating transaction status
                setTimeout(() => {
                  window.location.href = data.redirect_url || '/';
                }, 3000);
              }).catch(console.error);
            } else {
              // If no transaction ID, redirect immediately
              window.location.href = '/';
            }
          },
        },
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error: any) {
      setLoading(false);
      Swal.fire({
        icon: 'error',
        title: error.message || 'Payment initialization failed',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Active Subscription Alert */}
          {hasActiveSubscription && (
            <div className="mb-6 p-4 bg-orange-100 border-l-4 border-orange-500 rounded-r-lg">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-orange-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-orange-700 mb-1">
                    <span className="font-semibold">Active Subscription Found:</span> You currently have an active subscription to <span className="font-semibold">{activeSubscription?.plan_name}</span> plan that expires on {activeSubscription?.ends_at} ({activeSubscription?.days_remaining} days remaining).
                  </p>
                  <p className="text-sm text-orange-700">
                    To deactivate your current plan and purchase a new one, please contact the administrator at <a href="mailto:<EMAIL>" className="underline font-medium"><EMAIL></a>.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Header */}
          <div className="text-center mb-8">
            <img src="/assets/images/logo-icon.svg" alt="Salozy Logo" className="mx-auto w-16 mb-4" />
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Complete Your Subscription</h1>
            <p className="text-gray-600">You're just one step away from getting started with Salozy</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Plan Details */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">Plan Summary</h2>

              <div className="border-2 border-primary/20 rounded-xl p-6 mb-6">
                <h3 className="text-xl font-bold text-primary mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-4">{plan.description}</p>

                <div className="text-3xl font-bold text-gray-800 mb-4">
                  {plan.currency_symbol}{plan.price.toLocaleString()}
                  <span className="text-lg text-gray-500 font-normal">/{plan.billing_cycle}</span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Services:</span>
                    <span className="font-semibold">{plan.max_services === 0 ? 'Unlimited' : plan.max_services}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Branches:</span>
                    <span className="font-semibold">{plan.max_branches}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Staff Members:</span>
                    <span className="font-semibold">{plan.max_staff === 0 ? 'Unlimited' : plan.max_staff}</span>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between text-lg font-bold">
                  <span>Total Amount:</span>
                  <span className="text-primary">{plan.currency_symbol}{plan.price.toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* User Details & Payment */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                Confirm Your Details
              </h2>

              {authUser && (
                <div className="space-y-4 mb-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-700 mb-3">Account Information</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Name:</span>
                        <span className="font-medium">{authUser.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Email:</span>
                        <span className="font-medium">{authUser.email}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Phone:</span>
                        <span className="font-medium">{authUser.phone || 'Not provided'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <button
                onClick={handlePayment}
                disabled={loading || hasActiveSubscription}
                className="w-full bg-gradient-to-r from-primary to-purple-600 hover:from-primary-dark hover:to-purple-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Processing...' : hasActiveSubscription ? 'Already Subscribed' : `Pay ${plan.currency_symbol}${plan.price.toLocaleString()}`}
              </button>

              {hasActiveSubscription && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-700 mb-2">
                    <span className="font-semibold">Note:</span> You already have an active subscription to <span className="font-semibold">{activeSubscription?.plan_name}</span> plan.
                    It will expire on {activeSubscription?.ends_at} ({activeSubscription?.days_remaining} days remaining).
                  </p>
                  <p className="text-sm text-yellow-700">
                    To deactivate your current plan and purchase a new one, please contact the administrator at <a href="mailto:<EMAIL>" className="underline font-medium"><EMAIL></a>.
                  </p>
                </div>
              )}

              <p className="text-xs text-gray-500 text-center mt-4">
                By proceeding, you agree to our Terms of Service and Privacy Policy.
                Your payment is secured by Razorpay.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionCheckout;
