import { useEffect, useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import { IRootState } from '@/store';
// import ReactApexChart from 'react-apexcharts';
import PerfectScrollbar from 'react-perfect-scrollbar';
import Dropdown from '@/components/Dropdown';
import { setPageTitle } from '@/store/themeConfigSlice';

interface Props {
    appointmentStats: {
        today_total: number;
        today_pending: number;
        over_all_apoinment: number;
        today_in_progress: number;
        today_completed: number;
        today_cancelled: number;
    };
    seatStats: {
        total: number;
        available: number;
        occupied: number;
        maintenance: number;
        cleaning: number;
    };
    upcomingAppointments: Array<{
        id: number;
        customer_name: string;
        date: string;
        time: string;
        ticket_number: string;
        services_count: number;
        status: string;
    }>;
    recentCompletedServices: Array<{
        id: number;
        customer_name: string;
        service_name: string;
        seat: string;
        completed_at: string;
        duration: string;
    }>;
    revenueStats: {
        today: number;
        week: number;
        month: number;
        grand_total:number;
    };
    serviceAnalytics: Array<{
        service_name: string;
        total_bookings: number;
        total_revenue: number;
    }>;
    customerInsights: {
        total_customers: number;
        new_customers: number;
        repeat_customers: number;
    };
    monthlyRevenue: number[];
    currency_symbol?: string;
    currency_text?: string;
}

const Index = ({
    appointmentStats,
    seatStats,
    upcomingAppointments,
    recentCompletedServices,
    revenueStats,
    serviceAnalytics,
    customerInsights,
    monthlyRevenue,
    currency_symbol = '₹',
    currency_text = 'INR',
}: Props) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Salon Admin'));
    });
    const isDark = useSelector((state: IRootState) => state.themeConfig.theme === 'dark' || state.themeConfig.isDarkMode);
    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl' ? true : false;

    const [loading] = useState(false);

    const props = usePage().props as any;
    const currentbranch = props.auth.user.branch;

    const user = props.auth.user;

    // Revenue chart options
    const revenueChartOptions = {
        series: [{
            name: 'Revenue',
            data: [revenueStats.today, revenueStats.week, revenueStats.month]
        }],
        options: {
            chart: {
                height: 350,
                type: 'area',
                toolbar: {
                    show: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth'
            },
            xaxis: {
                categories: ['Today', 'This Week', 'This Month']
            },
            tooltip: {
                y: {
                    formatter: function (val: number) {
                        return currency_symbol + ' ' + val.toFixed(2)
                    }
                }
            }
        }
    };

    const areaChart: any = {
        series: [
            {
                name: 'Revenue',
                data: monthlyRevenue,
            },
        ],
        options: {
            chart: {
                type: 'area',
                height: 300,
                zoom: {
                    enabled: false,
                },
                toolbar: {
                    show: false,
                },
            },
            colors: ['#805dca'],
            dataLabels: {
                enabled: false,
            },
            stroke: {
                width: 2,
                curve: 'smooth',
            },
            xaxis: {
                axisBorder: {
                    color: isDark ? '#191e3a' : '#e0e6ed',
                },
            },
            yaxis: {
                opposite: isRtl ? true : false,
                labels: {
                    offsetX: isRtl ? -40 : 0,
                    formatter: function (val: number) {
                        return currency_symbol + ' ' + val.toFixed(2)
                    }
                },
            },
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            legend: {
                horizontalAlign: 'left',
            },
            grid: {
                borderColor: isDark ? '#191E3A' : '#E0E6ED',
                xaxis: {
                    lines: {
                        show: false,
                    },
                },
            },
            tooltip: {
                theme: isDark ? 'dark' : 'light',
                y: {
                    formatter: function (val: number) {
                        return currency_symbol + ' ' + val.toFixed(2)
                    }
                }
            },
        },
    };

    //Sales By Category
    const seatAvailabilityChart: any = {
        series: [seatStats.available, seatStats.occupied, seatStats.maintenance, seatStats.cleaning],
        options: {
            chart: {
                type: 'donut',
                height: 460,
                fontFamily: 'Nunito, sans-serif',
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 25,
                colors: isDark ? '#0e1726' : '#fff',
            },
            colors: isDark ? ['#1abc9c', '#e7515a', '#e2a03f', '#4361ee'] : ['#1abc9c', '#e7515a', '#e2a03f', '#4361ee'],
            legend: {
                position: 'bottom',
                horizontalAlign: 'center',
                fontSize: '14px',
                markers: {
                    width: 10,
                    height: 10,
                    offsetX: -2,
                },
                height: 50,
                offsetY: 20,
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '65%',
                        background: 'transparent',
                        labels: {
                            show: true,
                            name: {
                                show: true,
                                fontSize: '29px',
                                offsetY: -10,
                            },
                            value: {
                                show: true,
                                fontSize: '26px',
                                color: isDark ? '#bfc9d4' : undefined,
                                offsetY: 16,
                                formatter: (val: any) => {
                                    return val;
                                },
                            },
                            total: {
                                show: true,
                                label: 'Total Seats',
                                color: '#888ea8',
                                fontSize: '29px',
                                formatter: (w: any) => {
                                    return w.globals.seriesTotals.reduce(function (a: any, b: any) {
                                        return a + b;
                                    }, 0);
                                },
                            },
                        },
                    },
                },
            },
            labels: ['Available', 'Occupied', 'Maintenance', 'Cleaning'],
            states: {
                hover: {
                    filter: {
                        type: 'none',
                        value: 0.15,
                    },
                },
                active: {
                    filter: {
                        type: 'none',
                        value: 0.15,
                    },
                },
            },
        },
    };

    const getProfileImageUrl = (logoPath: string | null) => {
        if (!logoPath) return "/assets/images/profile-34.jpeg";

        // Get the current tenant's domain
        const tenantDomain = window.location.hostname;
        // Construct the full URL for the tenant's storage
        return `https://${tenantDomain}/${logoPath}`;
    };

    const activityIcons = [
        {
            icon: (
                <svg className="w-4 h-4" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
            ),
            bgColor: 'bg-secondary'
        },
        {
            icon: (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.5" d="M2 12C2 8.22876 2 6.34315 3.17157 5.17157C4.34315 4 6.22876 4 10 4H14C17.7712 4 19.6569 4 20.8284 5.17157C22 6.34315 22 8.22876 22 12C22 15.7712 22 17.6569 20.8284 18.8284C19.6569 20 17.7712 20 14 20H10C6.22876 20 4.34315 20 3.17157 18.8284C2 17.6569 2 15.7712 2 12Z" stroke="currentColor" strokeWidth="1.5"/>
                    <path d="M6 8L8.1589 9.79908C9.99553 11.3296 10.9139 12.0949 12 12.0949C13.0861 12.0949 14.0045 11.3296 15.8411 9.79908L18 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
            ),
            bgColor: 'bg-success'
        },
        {
            icon: (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.5" d="M4 12.9L7.14286 16.5L15 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M20.0002 7.5625L11.4286 16.5625L11.0002 16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
            ),
            bgColor: 'bg-primary'
        },
        {
            icon: (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15.3929 4.05365L14.8912 4.61112L15.3929 4.05365ZM19.3517 7.61654L18.85 8.17402L19.3517 7.61654ZM21.654 10.1541L20.9689 10.4592V10.4592L21.654 10.1541ZM3.17157 20.8284L3.7019 20.2981H3.7019L3.17157 20.8284ZM20.8284 20.8284L20.2981 20.2981L20.2981 20.2981L20.8284 20.8284ZM14 21.25H10V22.75H14V21.25ZM2.75 14V10H1.25V14H2.75ZM21.25 13.5629V14H22.75V13.5629H21.25ZM14.8912 4.61112L18.85 8.17402L19.8534 7.05907L15.8947 3.49618L14.8912 4.61112ZM22.75 13.5629C22.75 11.8745 22.7651 10.8055 22.3391 9.84897L20.9689 10.4592C21.2349 11.0565 21.25 11.742 21.25 13.5629H22.75ZM18.85 8.17402C20.2034 9.3921 20.7029 9.86199 20.9689 10.4592L22.3391 9.84897C21.9131 8.89241 21.1084 8.18853 19.8534 7.05907L18.85 8.17402ZM10.0298 2.75C11.6116 2.75 12.2085 2.76158 12.7405 2.96573L13.2779 1.5653C12.4261 1.23842 11.498 1.25 10.0298 1.25V2.75ZM15.8947 3.49618C14.8087 2.51878 14.1297 1.89214 13.2779 1.5653L12.7405 2.96573C13.2727 3.16993 13.7215 3.55836 14.8912 4.61112L15.8947 3.49618ZM10 21.25C8.09318 21.25 6.73851 21.2484 5.71085 21.1102C4.70476 20.975 4.12511 20.7213 3.7019 20.2981L2.64124 21.3588C3.38961 22.1071 4.33855 22.4392 5.51098 22.5969C6.66182 22.7516 8.13558 22.75 10 22.75V21.25ZM1.25 14C1.25 15.8644 1.24841 17.3382 1.40313 18.489C1.56076 19.6614 1.89288 20.6104 2.64124 21.3588L3.7019 20.2981C3.27869 19.8749 3.02502 19.2952 2.88976 18.2892C2.75159 17.2615 2.75 15.9068 2.75 14H1.25ZM14 22.75C15.8644 22.75 17.3382 22.7516 18.489 22.5969C19.6614 22.4392 20.6104 22.1071 21.3588 21.3588L20.2981 20.2981C19.8749 20.7213 19.2952 20.975 18.2892 21.1102C17.2615 21.2484 15.9068 21.25 14 21.25V22.75ZM21.25 14C21.25 15.9068 21.2484 17.2615 21.1102 18.2892C20.975 19.2952 20.7213 19.8749 20.2981 20.2981L21.3588 21.3588C22.1071 20.6104 22.4392 19.6614 22.5969 18.489C22.7516 17.3382 22.75 15.8644 22.75 14H21.25ZM2.75 10C2.75 8.09318 2.75159 6.73851 2.88976 5.71085C3.02502 4.70476 3.27869 4.12511 3.7019 3.7019L2.64124 2.64124C1.89288 3.38961 1.56076 4.33855 1.40313 5.51098C1.24841 6.66182 1.25 8.13558 1.25 10H2.75ZM10.0298 1.25C8.15538 1.25 6.67442 1.24842 5.51887 1.40307C4.34232 1.56054 3.39019 1.8923 2.64124 2.64124L3.7019 3.7019C4.12453 3.27928 4.70596 3.02525 5.71785 2.88982C6.75075 2.75158 8.11311 2.75 10.0298 2.75V1.25Z" fill="currentColor"/>
                    <path opacity="0.5" d="M13 2.5V5C13 7.35702 13 8.53553 13.7322 9.26777C14.4645 10 15.643 10 18 10H22" stroke="currentColor" strokeWidth="1.5"/>
                </svg>
            ),
            bgColor: 'bg-warning'
        },
        {
            icon: (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.5" d="M2 17C2 15.1144 2 14.1716 2.58579 13.5858C3.17157 13 4.11438 13 6 13H18C19.8856 13 20.8284 13 21.4142 13.5858C22 14.1716 22 15.1144 22 17C22 18.8856 22 19.8284 21.4142 20.4142C20.8284 21 19.8856 21 18 21H6C4.11438 21 3.17157 21 2.58579 20.4142C2 19.8284 2 18.8856 2 17Z" stroke="currentColor" strokeWidth="1.5"/>
                    <path opacity="0.5" d="M2 6C2 4.11438 2 3.17157 2.58579 2.58579C3.17157 2 4.11438 2 6 2H18C19.8856 2 20.8284 2 21.4142 2.58579C22 3.17157 22 4.11438 22 6C22 7.88562 22 8.82843 21.4142 9.41421C20.8284 10 19.8856 10 18 10H6C4.11438 10 3.17157 10 2.58579 9.41421C2 8.82843 2 7.88562 2 6Z" stroke="currentColor" strokeWidth="1.5"/>
                    <path d="M11 6H18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M6 6H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M11 17H18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                    <path d="M6 17H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
            ),
            bgColor: 'bg-dark'
        }
    ];

    const getRandomIcon = () => {
        return activityIcons[Math.floor(Math.random() * activityIcons.length)];
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Salon Admin</span>
                </li>
            </ul>

            <div className="pt-5">
                {/* Revenue Overview */}
                <div className="gap-6 grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 mb-6">
                    <div className="bg-gradient-to-r from-cyan-500 to-cyan-400 panel">
                        <div className="flex justify-between">
                            <div className="ltr:mr-1 rtl:ml-1 font-semibold text-md text-white">Today's Revenue</div>
                        </div>
                        <div className="flex items-center mt-5">
                            <div className="ltr:mr-3 rtl:ml-3 font-bold text-3xl text-white">{currency_symbol}{(revenueStats?.today || 0).toFixed(2)}</div>
                        </div>
                    </div>
                    <div className="bg-gradient-to-r from-violet-500 to-violet-400 panel">
                        <div className="flex justify-between">
                            <div className="ltr:mr-1 rtl:ml-1 font-semibold text-md text-white">Weekly Revenue</div>
                        </div>
                        <div className="flex items-center mt-5">
                            <div className="ltr:mr-3 rtl:ml-3 font-bold text-3xl text-white">{currency_symbol}{(revenueStats?.week || 0).toFixed(2)}</div>
                        </div>
                    </div>
                    <div className="bg-gradient-to-r from-blue-500 to-blue-400 panel">
                        <div className="flex justify-between">
                            <div className="ltr:mr-1 rtl:ml-1 font-semibold text-md text-white">Monthly Revenue</div>
                        </div>
                        <div className="flex items-center mt-5">
                            <div className="ltr:mr-3 rtl:ml-3 font-bold text-3xl text-white">{currency_symbol}{(revenueStats?.month || 0).toFixed(2)}</div>
                        </div>
                    </div>
                    <div className="bg-gradient-to-r from-fuchsia-500 to-fuchsia-400 panel">
                        <div className="flex justify-between">
                            <div className="ltr:mr-1 rtl:ml-1 font-semibold text-md text-white">Total Customers</div>
                        </div>
                        <div className="flex items-center mt-5">
                            <div className="ltr:mr-3 rtl:ml-3 font-bold text-3xl text-white">{customerInsights.total_customers}</div>
                        </div>
                    </div>
                </div>

                {/* Appointments and Seats */}
                <div className="gap-6 grid xl:grid-cols-3 mb-6">


                <div className="panel">
                        <div className="flex justify-between items-center mb-5 dark:text-white-light">
                            <h5 className="font-semibold text-lg">Customer Insights</h5>
                        </div>
                        <div className="space-y-9">
                            <div className="flex items-center">
                                <div className="ltr:mr-3 rtl:ml-3 w-9 h-9">
                                    <div className="place-content-center grid bg-secondary-light dark:bg-secondary rounded-full w-9 h-9 text-secondary dark:text-secondary-light">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M3.74157 18.5545C4.94119 20 7.17389 20 11.6393 20H12.3605C16.8259 20 19.0586 20 20.2582 18.5545M3.74157 18.5545C2.54194 17.1091 2.9534 14.9146 3.77633 10.5257C4.36155 7.40452 4.65416 5.84393 5.76506 4.92196M3.74157 18.5545C3.74156 18.5545 3.74157 18.5545 3.74157 18.5545ZM20.2582 18.5545C21.4578 17.1091 21.0464 14.9146 20.2235 10.5257C19.6382 7.40452 19.3456 5.84393 18.2347 4.92196M20.2582 18.5545C20.2582 18.5545 20.2582 18.5545 20.2582 18.5545ZM18.2347 4.92196C17.1238 4 15.5361 4 12.3605 4H11.6393C8.46374 4 6.87596 4 5.76506 4.92196M18.2347 4.92196C18.2347 4.92196 18.2347 4.92196 18.2347 4.92196ZM5.76506 4.92196C5.76506 4.92196 5.76506 4.92196 5.76506 4.92196Z"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                            <path
                                                opacity="0.5"
                                                d="M9.1709 8C9.58273 9.16519 10.694 10 12.0002 10C13.3064 10 14.4177 9.16519 14.8295 8"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                                strokeLinecap="round"
                                            />
                                        </svg>
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <div className="flex mb-2 font-semibold text-white-dark">
                                        <h6>Total Customers</h6>
                                        <p className="rtl:mr-auto ltr:ml-auto">{customerInsights.total_customers}</p>
                                    </div>
                                    <div className="bg-dark-light dark:bg-[#1b2e4b] shadow rounded-full h-2">
                                        <div className="bg-gradient-to-r from-[#7579ff] to-[#b224ef] rounded-full h-full" style={{ width: '100%' }}></div>
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center">
                                <div className="ltr:mr-3 rtl:ml-3 w-9 h-9">
                                    <div className="place-content-center grid bg-success-light dark:bg-success rounded-full w-9 h-9 text-success dark:text-success-light">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M4.72848 16.1369C3.18295 14.5914 2.41018 13.8186 2.12264 12.816C1.83509 11.8134 2.08083 10.7485 2.57231 8.61875L2.85574 7.39057C3.26922 5.59881 3.47597 4.70292 4.08944 4.08944C4.70292 3.47597 5.59881 3.26922 7.39057 2.85574L8.61875 2.57231C10.7485 2.08083 11.8134 1.83509 12.816 2.12264C13.8186 2.41018 14.5914 3.18295 16.1369 4.72848L17.9665 6.55812C20.6555 9.24711 22 10.5916 22 12.2623C22 13.933 20.6555 15.2775 17.9665 17.9665C15.2775 20.6555 13.933 22 12.2623 22C10.5916 22 9.24711 20.6555 6.55812 17.9665L4.72848 16.1369Z"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                            <circle opacity="0.5" cx="8.60699" cy="8.87891" r="2" transform="rotate(-45 8.60699 8.87891)" stroke="currentColor" strokeWidth="1.5" />
                                            <path opacity="0.5" d="M11.5417 18.5L18.5208 11.5208" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <div className="flex mb-2 font-semibold text-white-dark">
                                        <h6>New Customers</h6>
                                        <p className="rtl:mr-auto ltr:ml-auto">{customerInsights.new_customers}</p>
                                    </div>
                                    <div className="bg-dark-light dark:bg-[#1b2e4b] shadow rounded-full w-full h-2">
                                        <div className="bg-gradient-to-r from-[#3cba92] to-[#0ba360] rounded-full h-full" style={{ width: `${(customerInsights.new_customers / customerInsights.total_customers) * 100}%` }}></div>
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center">
                                <div className="ltr:mr-3 rtl:ml-3 w-9 h-9">
                                    <div className="place-content-center grid bg-warning-light dark:bg-warning rounded-full w-9 h-9 text-warning dark:text-warning-light">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M2 12C2 8.22876 2 6.34315 3.17157 5.17157C4.34315 4 6.22876 4 10 4H14C17.7712 4 19.6569 4 20.8284 5.17157C22 6.34315 22 8.22876 22 12C22 15.7712 22 17.6569 20.8284 18.8284C19.6569 20 17.7712 20 14 20H10C6.22876 20 4.34315 20 3.17157 18.8284C2 17.6569 2 15.7712 2 12Z"
                                                stroke="currentColor"
                                                strokeWidth="1.5"
                                            />
                                            <path opacity="0.5" d="M10 16H6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                            <path opacity="0.5" d="M14 16H12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                            <path opacity="0.5" d="M2 10L22 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
                                        </svg>
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <div className="flex mb-2 font-semibold text-white-dark">
                                        <h6>Repeat Customers</h6>
                                        <p className="rtl:mr-auto ltr:ml-auto">{customerInsights.repeat_customers}</p>
                                    </div>
                                    <div className="bg-dark-light dark:bg-[#1b2e4b] shadow rounded-full w-full h-2">
                                        <div className="bg-gradient-to-r from-[#f09819] to-[#ff5858] rounded-full h-full" style={{ width: `${(customerInsights.repeat_customers / customerInsights.total_customers) * 100}%` }}></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>

                    {/* <div className="h-full panel">
                        <div className="flex items-center mb-5">
                            <h5 className="font-semibold text-lg dark:text-white-light">Seat Availability</h5>
                        </div>
                        <div>
                            <div className="bg-white dark:bg-black rounded-lg overflow-hidden">
                                {loading ? (
                                    <div className="place-content-center grid bg-white-light/30 dark:bg-dark dark:bg-opacity-[0.08] min-h-[325px]">
                                        <span className="inline-flex border-2 dark:border-white border-black !border-l-transparent rounded-full w-5 h-5 animate-spin"></span>
                                    </div>
                                ) : (
                                    <ReactApexChart series={seatAvailabilityChart.series} options={seatAvailabilityChart.options} type="donut" height={460} />
                                )}
                            </div>
                        </div>
                    </div> */}




                    <div className="p-0 border-0 h-full overflow-hidden panel">
                        <div className="bg-gradient-to-r from-[#4361ee] to-[#160f6b] p-6 min-h-[190px]">
                            <div className="flex justify-between items-center mb-6">
                                <div className="flex items-center bg-black/50 p-1 ltr:pr-3 rtl:pl-3 rounded-full font-semibold text-white">
                                    <img className="block ltr:mr-1 rtl:ml-1 border-2 border-white/50 rounded-full w-8 h-8 object-cover" src={getProfileImageUrl(user.logo)} alt="avatar" />
                                    {currentbranch.name}
                                </div>
                                <button type="button" className="flex justify-between items-center bg-black hover:opacity-80 rtl:mr-auto ltr:ml-auto rounded-md w-9 h-9 text-white">
                                    <svg className="m-auto w-6 h-6" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                </button>
                            </div>
                            <div className="flex justify-between items-center text-white">
                                <p className="text-xl">Total Revenue</p>
                                <h5 className="rtl:mr-auto ltr:ml-auto text-2xl">
                                    <span className="text-white-light">{currency_symbol}</span> {(revenueStats?.grand_total || 0).toFixed(2)}
                                </h5>
                            </div>
                        </div>
                        <div className="gap-2 grid grid-cols-2 -mt-12 px-8">
                            <div className="bg-white dark:bg-[#060818] shadow px-4 py-2.5 rounded-md">
                                <span className="flex justify-between items-center mb-4 dark:text-white">
                                    Today's Appointment
                                    <svg className="w-4 h-4 text-success" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M19 15L12 9L5 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </span>
                                <div className="bg-[#ebedf2] dark:bg-black shadow-none py-1 border-0 w-full text-[#515365] text-base dark:text-[#bfc9d4] btn">{appointmentStats.today_total}</div>
                            </div>
                            <div className="bg-white dark:bg-[#060818] shadow px-4 py-2.5 rounded-md">
                                <span className="flex justify-between items-center mb-4 dark:text-white">
                                    Total Appointment
                                    <svg className="w-4 h-4 text-danger" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M19 9L12 15L5 9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                </span>
                                <div className="bg-[#ebedf2] dark:bg-black shadow-none py-1 border-0 w-full text-[#515365] text-base dark:text-[#bfc9d4] btn">{appointmentStats.over_all_apoinment}</div>
                            </div>
                        </div>
                        <div className="p-5">
                            <div className="mb-5">
                                <span className="before:inline-block bg-[#1b2e4b] before:bg-white ltr:before:mr-2 rtl:before:ml-2 px-4 py-1.5 rounded-full before:rounded-full before:w-1.5 before:h-1.5 text-white text-xs">
                                    Today's Appoinment
                                </span>
                            </div>
                            <div className="space-y-1 mb-5">
                                <div className="flex justify-between items-center">
                                    <p className="font-semibold text-[#515365]">Pending</p>
                                    <p className="text-base">
                                         <span className="font-semibold">{appointmentStats.today_pending}</span>
                                    </p>
                                </div>


                                <div className="flex justify-between items-center">
                                    <p className="font-semibold text-[#515365]">In Progress</p>
                                    <p className="text-base">
                                         <span className="font-semibold">{appointmentStats.today_in_progress}</span>
                                    </p>
                                </div>

                                <div className="flex justify-between items-center">
                                    <p className="font-semibold text-[#515365]">Completed</p>
                                    <p className="text-base">
                                         <span className="font-semibold">{appointmentStats.today_completed}</span>
                                    </p>
                                </div>

                            </div>
                            <div className="flex justify-around px-2 text-center">
                                <Link href={route('vendor.appointments.index')} className="ltr:mr-2 rtl:ml-2 btn btn-secondary">
                                    View All Appointment
                                </Link>
                            </div>
                        </div>
                    </div>



                    <div className="panel">
                        <div className="flex justify-between items-start -mx-5 mb-5 p-5 pt-0 border-white-light dark:border-[#1b2e4b] border-b dark:text-white-light">
                            <h5 className="font-semibold text-lg">Recent Activity</h5>
                        </div>
                        <PerfectScrollbar className="relative ltr:-mr-3 rtl:-ml-3 ltr:pr-3 rtl:pl-3 h-[360px] perfect-scrollbar">
                            <div className="space-y-7">
                                {recentCompletedServices.map((service) => {
                                    const icons = [
                                        {
                                            icon: (
                                                <svg className="w-4 h-4" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                                </svg>
                                            ),
                                            bgColor: 'bg-secondary'
                                        },
                                        {
                                            icon: (
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.5" d="M2 12C2 8.22876 2 6.34315 3.17157 5.17157C4.34315 4 6.22876 4 10 4H14C17.7712 4 19.6569 4 20.8284 5.17157C22 6.34315 22 8.22876 22 12C22 15.7712 22 17.6569 20.8284 18.8284C19.6569 20 17.7712 20 14 20H10C6.22876 20 4.34315 20 3.17157 18.8284C2 17.6569 2 15.7712 2 12Z" stroke="currentColor" strokeWidth="1.5"/>
                                                    <path d="M6 8L8.1589 9.79908C9.99553 11.3296 10.9139 12.0949 12 12.0949C13.0861 12.0949 14.0045 11.3296 15.8411 9.79908L18 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                                                </svg>
                                            ),
                                            bgColor: 'bg-success'
                                        },
                                        {
                                            icon: (
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.5" d="M4 12.9L7.14286 16.5L15 7.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                    <path d="M20.0002 7.5625L11.4286 16.5625L11.0002 16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                                </svg>
                                            ),
                                            bgColor: 'bg-primary'
                                        },
                                        {
                                            icon: (
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M15.3929 4.05365L14.8912 4.61112L15.3929 4.05365ZM19.3517 7.61654L18.85 8.17402L19.3517 7.61654ZM21.654 10.1541L20.9689 10.4592V10.4592L21.654 10.1541ZM3.17157 20.8284L3.7019 20.2981H3.7019L3.17157 20.8284ZM20.8284 20.8284L20.2981 20.2981L20.2981 20.2981L20.8284 20.8284ZM14 21.25H10V22.75H14V21.25ZM2.75 14V10H1.25V14H2.75ZM21.25 13.5629V14H22.75V13.5629H21.25ZM14.8912 4.61112L18.85 8.17402L19.8534 7.05907L15.8947 3.49618L14.8912 4.61112ZM22.75 13.5629C22.75 11.8745 22.7651 10.8055 22.3391 9.84897L20.9689 10.4592C21.2349 11.0565 21.25 11.742 21.25 13.5629H22.75ZM18.85 8.17402C20.2034 9.3921 20.7029 9.86199 20.9689 10.4592L22.3391 9.84897C21.9131 8.89241 21.1084 8.18853 19.8534 7.05907L18.85 8.17402ZM10.0298 2.75C11.6116 2.75 12.2085 2.76158 12.7405 2.96573L13.2779 1.5653C12.4261 1.23842 11.498 1.25 10.0298 1.25V2.75ZM15.8947 3.49618C14.8087 2.51878 14.1297 1.89214 13.2779 1.5653L12.7405 2.96573C13.2727 3.16993 13.7215 3.55836 14.8912 4.61112L15.8947 3.49618ZM10 21.25C8.09318 21.25 6.73851 21.2484 5.71085 21.1102C4.70476 20.975 4.12511 20.7213 3.7019 20.2981L2.64124 21.3588C3.38961 22.1071 4.33855 22.4392 5.51098 22.5969C6.66182 22.7516 8.13558 22.75 10 22.75V21.25ZM1.25 14C1.25 15.8644 1.24841 17.3382 1.40313 18.489C1.56076 19.6614 1.89288 20.6104 2.64124 21.3588L3.7019 20.2981C3.27869 19.8749 3.02502 19.2952 2.88976 18.2892C2.75159 17.2615 2.75 15.9068 2.75 14H1.25ZM14 22.75C15.8644 22.75 17.3382 22.7516 18.489 22.5969C19.6614 22.4392 20.6104 22.1071 21.3588 21.3588L20.2981 20.2981C19.8749 20.7213 19.2952 20.975 18.2892 21.1102C17.2615 21.2484 15.9068 21.25 14 21.25V22.75ZM21.25 14C21.25 15.9068 21.2484 17.2615 21.1102 18.2892C20.975 19.2952 20.7213 19.8749 20.2981 20.2981L21.3588 21.3588C22.1071 20.6104 22.4392 19.6614 22.5969 18.489C22.7516 17.3382 22.75 15.8644 22.75 14H21.25ZM2.75 10C2.75 8.09318 2.75159 6.73851 2.88976 5.71085C3.02502 4.70476 3.27869 4.12511 3.7019 3.7019L2.64124 2.64124C1.89288 3.38961 1.56076 4.33855 1.40313 5.51098C1.24841 6.66182 1.25 8.13558 1.25 10H2.75ZM10.0298 1.25C8.15538 1.25 6.67442 1.24842 5.51887 1.40307C4.34232 1.56054 3.39019 1.8923 2.64124 2.64124L3.7019 3.7019C4.12453 3.27928 4.70596 3.02525 5.71785 2.88982C6.75075 2.75158 8.11311 2.75 10.0298 2.75V1.25Z" fill="currentColor"/>
                                                    <path opacity="0.5" d="M13 2.5V5C13 7.35702 13 8.53553 13.7322 9.26777C14.4645 10 15.643 10 18 10H22" stroke="currentColor" strokeWidth="1.5"/>
                                                </svg>
                                            ),
                                            bgColor: 'bg-warning'
                                        },
                                        {
                                            icon: (
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.5" d="M2 17C2 15.1144 2 14.1716 2.58579 13.5858C3.17157 13 4.11438 13 6 13H18C19.8856 13 20.8284 13 21.4142 13.5858C22 14.1716 22 15.1144 22 17C22 18.8856 22 19.8284 21.4142 20.4142C20.8284 21 19.8856 21 18 21H6C4.11438 21 3.17157 21 2.58579 20.4142C2 19.8284 2 18.8856 2 17Z" stroke="currentColor" strokeWidth="1.5"/>
                                                    <path opacity="0.5" d="M2 6C2 4.11438 2 3.17157 2.58579 2.58579C3.17157 2 4.11438 2 6 2H18C19.8856 2 20.8284 2 21.4142 2.58579C22 3.17157 22 4.11438 22 6C22 7.88562 22 8.82843 21.4142 9.41421C20.8284 10 19.8856 10 18 10H6C4.11438 10 3.17157 10 2.58579 9.41421C2 8.82843 2 7.88562 2 6Z" stroke="currentColor" strokeWidth="1.5"/>
                                                    <path d="M11 6H18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                                                    <path d="M6 6H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                                                    <path d="M11 17H18" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                                                    <path d="M6 17H8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                                                </svg>
                                            ),
                                            bgColor: 'bg-dark'
                                        }
                                    ];
                                    const randomIcon = icons[Math.floor(Math.random() * icons.length)];

                                    return (
                                        <div key={service.id} className="flex">
                                            <div className="relative before:top-10 before:left-4 z-10 before:absolute before:bg-white-dark/30 ltr:mr-2 rtl:ml-2 before:w-[2px] before:h-[calc(100%-24px)] shrink-0">
                                                <div className={`flex justify-center items-center ${randomIcon.bgColor} shadow shadow-${randomIcon.bgColor.split('-')[1]} rounded-full w-8 h-8 text-white`}>
                                                    {randomIcon.icon}
                                                </div>
                                            </div>
                                            <div>
                                                <h5 className="font-semibold dark:text-white-light">
                                                    Service Completed: <span className="text-success">{service.service_name}</span>
                                                </h5>
                                                <div className="text-xs text-gray-500">
                                                    <span className="font-semibold">{service.customer_name}</span> - {service.seat}
                                                </div>
                                                <p className="text-white-dark text-xs">{service.completed_at}</p>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </PerfectScrollbar>
                    </div>


                    <div className="h-full panel">
                        <div className="flex justify-between mb-5 dark:text-white-light">
                            <h5 className="font-semibold text-lg">Top Services</h5>
                        </div>
                        <div className="table-responsive">
                            <table>
                                <thead>
                                    <tr className='border-b-0'>
                                        <th>Service</th>
                                        <th>Bookings</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {serviceAnalytics.map((service, index) => (
                                        <tr key={index} className='group text-white-dark hover:text-black dark:hover:text-white-light/90'>
                                            <td>{service.service_name}</td>
                                            <td>{service.total_bookings}</td>
                                            <td>{currency_symbol}{(service?.total_revenue || 0).toFixed(2)}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                {/* Customer Insights and Recent Activity */}
                {/*<div className="gap-6 grid grid-cols-1 xl:grid-cols-2  mb-6">*/}

                {/*    <div className="panel">*/}
                {/*        <div className="mb-5 flex items-center justify-between">*/}
                {/*            <h5 className="text-lg font-semibold dark:text-white">Monthly Revenue of current Year</h5>*/}
                {/*            */}
                {/*        </div>*/}
                {/*        <div className="mb-5">*/}
                {/*            <ReactApexChart series={areaChart.series} options={areaChart.options} className="rounded-lg bg-white dark:bg-black overflow-hidden" type="area" height={300} />*/}
                {/*        </div>*/}
                {/*    </div>*/}

                {/*</div>*/}



                {/* Revenue Chart and Service Analytics */}
                <div className="w-full mb-6">



{/*
                    <div className="panel">
                        <div className="flex justify-between mb-5 dark:text-white-light">
                            <h5 className="font-semibold text-lg">Revenue Overview</h5>
                        </div>
                        <ReactApexChart series={revenueChartOptions.series} options={revenueChartOptions.options} type="area" height={350} />
                    </div> */}

                </div>

            </div>
        </div>
    );
};

export default Index;


