<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class <PERSON>el extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Run service reminder check daily at 9 AM
        // $schedule->command('app:send-service-reminders')
        //     ->dailyAt('09:00')
        //     ->withoutOverlapping()
        //     ->appendOutputTo(storage_path('logs/service-reminders.log'));

        // $schedule->command('app:send-service-reminders')
        // ->cron('0 6,12 * * *') // At 6:00 AM and 12:00 PM
        // ->withoutOverlapping()
        // ->appendOutputTo(storage_path('logs/service-reminders.log'));

        $schedule->command('app:send-service-reminders')->hourly()->withoutOverlapping()->appendOutputTo(storage_path('logs/service-reminders.log'));

    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
