<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // vendor user
            $table->foreignId('subscription_plan_id')->constrained()->onDelete('cascade');

            $table->datetime('starts_at');
            $table->datetime('ends_at');
            $table->datetime('trial_ends_at')->nullable();

            $table->enum('status', ['active', 'inactive', 'cancelled', 'expired', 'trial'])->default('trial');

            // Usage tracking for current billing period
            $table->integer('current_services_count')->default(0);
            $table->integer('current_appointments_count')->default(0);
            $table->integer('current_seats_count')->default(0);
            $table->integer('current_branches_count')->default(0);
            $table->integer('current_staff_count')->default(0);

            // Billing period tracking
            $table->date('billing_period_start')->nullable();
            $table->date('billing_period_end')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Ensure one active subscription per vendor
            $table->unique(['user_id', 'status'], 'unique_active_subscription');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_subscriptions');
    }
};
