import React, { useEffect, useState, useCallback } from 'react';
import { Link } from '@inertiajs/react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import axios from 'axios';
import Swal from 'sweetalert2';
import debounce from 'lodash/debounce';

const Calendar = () => {
    const [events, setEvents] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [currentView, setCurrentView] = useState('dayGridMonth');

    // Debounced fetch function to prevent multiple rapid calls
    const debouncedFetchEvents = useCallback(
        debounce(async (start: string, end: string) => {
            try {
                setLoading(true);
                setError(null);
                
                const response = await axios.get('/vendor/appointments/calendar-data', {
                    params: { start, end }
                });

                if (response.data.success) {
                    setEvents(response.data.data);
                } else {
                    setError(response.data.message || 'Failed to load appointments');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: response.data.message || 'Failed to load appointments',
                        timer: 3000,
                        showConfirmButton: false
                    });
                }
            } catch (err) {
                console.error('Error fetching events:', err);
                setError('Failed to load appointments');
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load appointments. Please try again later.',
                    timer: 3000,
                    showConfirmButton: false
                });
            } finally {
                setLoading(false);
            }
        }, 500), // 500ms debounce delay
        []
    );

    // Initial load
    useEffect(() => {
        const now = new Date();
        const end = new Date();
        end.setMonth(end.getMonth() + 3);
        
        debouncedFetchEvents(
            now.toISOString(),
            end.toISOString()
        );

        // Cleanup function to cancel any pending debounced calls
        return () => {
            debouncedFetchEvents.cancel();
        };
    }, [debouncedFetchEvents]);

    const handleDatesSet = (info: any) => {
        // Only fetch if the view has actually changed
        if (info.view.type !== currentView) {
            setCurrentView(info.view.type);
            debouncedFetchEvents(
                info.startStr,
                info.endStr
            );
        }
    };

    const handleEventClick = (info: any) => {
        const event = info.event;
        const props = event.extendedProps;
        
        // Format status text
        const formatStatus = (status: string) => {
            return status
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
        };

        // Get status color
        const getStatusColor = (status: string) => {
            const colors = {
                'pending': '#FFA500',
                'in_progress': '#1E90FF',
                'completed': '#32CD32',
                'cancelled': '#FF0000'
            };
            return colors[status] || '#808080';
        };

        Swal.fire({
            title: `<div class="text-xl font-bold mb-4">${event.title}</div>`,
            html: `
                <div class="text-left space-y-4">
                    <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                        <span class="font-semibold text-gray-600">Ticket Number:</span>
                        <span class="font-mono bg-gray-100 px-3 py-1 rounded">${props.ticket_number}</span>
                    </div>
                    
                    <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                        <span class="font-semibold text-gray-600">Status:</span>
                        <span class="px-3 py-1 rounded text-white" style="background-color: ${getStatusColor(props.status)}">
                            ${formatStatus(props.status)}
                        </span>
                    </div>
                    
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="font-semibold text-gray-600 mb-2">Services:</div>
                        <div class="flex flex-wrap gap-2">
                            ${props.services.map((service: string) => `
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                                    ${service}
                                </span>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <div class="font-semibold text-gray-600 mb-1">Phone</div>
                            <div class="text-gray-800">${props.customer_phone}</div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <div class="font-semibold text-gray-600 mb-1">Email</div>
                            <div class="text-gray-800">${props.customer_email}</div>
                        </div>
                    </div>
                    
                    ${props.notes ? `
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <div class="font-semibold text-gray-600 mb-1">Notes</div>
                            <div class="text-gray-800">${props.notes}</div>
                        </div>
                    ` : ''}
                </div>
            `,
            showConfirmButton: true,
            confirmButtonText: 'Close',
            confirmButtonColor: '#3085d6',
            customClass: {
                popup: 'appointment-details-popup',
                title: 'appointment-details-title',
                htmlContainer: 'appointment-details-content',
                confirmButton: 'appointment-details-button'
            },
            width: '600px',
            padding: '2rem',
            background: '#ffffff',
            backdrop: `
                rgba(0,0,0,0.4)
                left top
                no-repeat
            `
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Appointments Calendar</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <Link href="/vendor/appointments" className="btn btn-primary">
                            <svg className="w-5 h-5 ltr:mr-2 rtl:ml-2" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                <line x1="19" y1="12" x2="5" y2="12"></line>
                                <polyline points="12 19 5 12 12 5"></polyline>
                            </svg>
                            Back to List View
                        </Link>
                    </div>

                    <div className="calendar-container">
                        {loading && (
                            <div className="flex items-center justify-center p-4">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                            </div>
                        )}
                        {error && (
                            <div className="bg-danger/20 text-danger border border-danger/20 rounded-md p-4 mb-5">
                                <div className="flex">
                                    <div className="flex-1">
                                        {error}
                                        <button 
                                            className="ml-2 text-danger hover:text-danger-dark"
                                            onClick={() => {
                                                const now = new Date();
                                                const end = new Date();
                                                end.setMonth(end.getMonth() + 3);
                                                debouncedFetchEvents(now.toISOString(), end.toISOString());
                                            }}
                                        >
                                            Retry
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                        <FullCalendar
                            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                            initialView="dayGridMonth"
                            headerToolbar={{
                                left: 'prev,next today',
                                center: 'title',
                                right: 'dayGridMonth,timeGridWeek,timeGridDay'
                            }}
                            events={events}
                            editable={false}
                            selectable={true}
                            selectMirror={true}
                            dayMaxEvents={true}
                            weekends={true}
                            height="auto"
                            dateClick={(info) => {
                                // Handle date click if needed
                            }}
                            eventClick={handleEventClick}
                            datesSet={handleDatesSet}
                            validRange={{
                                start: new Date(),
                                end: new Date(new Date().setFullYear(new Date().getFullYear() + 1))
                            }}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Calendar;
