import { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import i18next from 'i18next';
import Dropdown from '@components/Dropdown';
import { IRootState } from '@store';
import { setPageTitle, toggleRTL } from '@store/themeConfigSlice';
import { authImages } from '@images';

const RegisterBoxed = ({ errors = {} }) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Register Boxed'));
    });
    const isDark = useSelector((state: IRootState) => state.themeConfig.theme === 'dark' || state.themeConfig.isDarkMode);
    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl' ? true : false;
    const themeConfig = useSelector((state: IRootState) => state.themeConfig);
    const centralDomain = import.meta.env.VITE_CENTRAL_DOMAIN || 'salozy.com';

    const setLocale = (flag: string) => {
        setFlag(flag);
        if (flag.toLowerCase() === 'ae') {
            dispatch(toggleRTL('rtl'));
        } else {
            dispatch(toggleRTL('ltr'));
        }
    };
    const [flag, setFlag] = useState(themeConfig.locale);

    const [formData, setFormData] = useState({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        role: 'customer', // Default role
        company_name: '',
        company_domain: '',
    });

    const [showVendorFields, setShowVendorFields] = useState(false);
    const [processing, setProcessing] = useState(false);

    const handleChange = (e) => {
        const { id, value, type, checked } = e.target;
        let newValue = type === 'checkbox' ? checked : value;

        if (id === 'company_domain') {
            newValue = newValue
                .replace(/\s+/g, '-')        // Replace spaces with dashes
                .replace(/[^a-zA-Z0-9-]/g, '') // Remove special characters except dash
                .toLowerCase();              // Convert to lowercase
        }

        setFormData(prev => ({
            ...prev,
            [id]: newValue
        }));

        // Show/hide vendor fields when role changes
        if (id === 'role') {
            setShowVendorFields(value === 'vendor');
        }
    };

    const setRole = (role) => {
        setFormData(prev => ({
            ...prev,
            role
        }));
        setShowVendorFields(role === 'vendor');
    };

    const submitForm = (e) => {
        e.preventDefault();
        setProcessing(true);
        router.post('/auth/register', formData, {
            onFinish: () => setProcessing(false)
        });
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
            {/* Subtle animated background elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-20 left-10 w-32 h-32 bg-orange-800/5 rounded-full animate-float-slow"></div>
                <div className="absolute top-40 right-20 w-24 h-24 bg-gray-400/5 rounded-full animate-float-medium"></div>
                <div className="absolute bottom-40 left-20 w-40 h-40 bg-orange-800/5 rounded-full animate-float-slow"></div>
                <div className="absolute bottom-20 right-10 w-28 h-28 bg-gray-400/5 rounded-full animate-float-fast"></div>
            </div>

            <div className="relative flex justify-center items-center px-4 py-8 min-h-screen">
                <div className="w-full max-w-4xl">
                    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/50 rounded-3xl shadow-2xl p-8 md:p-12 transform transition-all duration-300 hover:shadow-3xl">

                        {/* Header Section */}
                        <div className="text-center mb-12">
                            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-orange-800 to-orange-900 rounded-3xl mb-6 shadow-lg">
                                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                </svg>
                            </div>
                            <h1 className="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">
                                Create Your Account
                            </h1>
                            <p className="text-lg text-gray-600 dark:text-gray-400 font-medium max-w-2xl mx-auto leading-relaxed">
                                Join thousands of users and start your journey with us today. Choose your role and get started in minutes.
                            </p>
                        </div>

                        {/* Role Selection Cards */}
                        <div className="mb-12">
                            <div className="text-center mb-8">
                                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                    Choose Your Role
                                </h2>
                                <p className="text-gray-600 dark:text-gray-400">
                                    Select how you want to use our platform
                                </p>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-3xl mx-auto">
                                <div
                                    className={`cursor-pointer rounded-3xl p-8 border-2 transition-all duration-300 flex flex-col items-center transform hover:scale-105 hover:shadow-xl ${formData.role === 'customer'
                                        ? 'border-orange-800 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/10 text-orange-800 dark:text-orange-600 shadow-xl ring-4 ring-orange-800/20'
                                        : 'border-gray-200 dark:border-gray-600 hover:border-orange-300 dark:hover:border-orange-700 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:shadow-lg'
                                        }`}
                                    onClick={() => setRole('customer')}
                                >
                                    <div className={`p-4 rounded-2xl mb-4 transition-all duration-300 ${formData.role === 'customer' ? 'bg-orange-800 text-white shadow-lg' : 'bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300'}`}>
                                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12" cy="8" r="4" fill="currentColor" />
                                            <path d="M20 19C20 16.7909 16.4183 15 12 15C7.58172 15 4 16.7909 4 19V21H20V19Z" fill="currentColor" />
                                        </svg>
                                    </div>
                                    <h3 className="font-bold text-xl mb-2">Customer</h3>
                                    <p className="text-center text-sm opacity-80 leading-relaxed">
                                        Book appointments, manage your bookings, and enjoy premium services from verified providers
                                    </p>
                                    {formData.role === 'customer' && (
                                        <div className="mt-4 flex items-center text-orange-800 dark:text-orange-600">
                                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="font-semibold">Selected</span>
                                        </div>
                                    )}
                                </div>

                                <div
                                    className={`cursor-pointer rounded-3xl p-8 border-2 transition-all duration-300 flex flex-col items-center transform hover:scale-105 hover:shadow-xl ${formData.role === 'vendor'
                                        ? 'border-orange-800 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/10 text-orange-800 dark:text-orange-600 shadow-xl ring-4 ring-orange-800/20'
                                        : 'border-gray-200 dark:border-gray-600 hover:border-orange-300 dark:hover:border-orange-700 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:shadow-lg'
                                        }`}
                                    onClick={() => setRole('vendor')}
                                >
                                    <div className={`p-4 rounded-2xl mb-4 transition-all duration-300 ${formData.role === 'vendor' ? 'bg-orange-800 text-white shadow-lg' : 'bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300'}`}>
                                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 11.5V8.8C21 7.11984 21 6.27976 20.673 5.63803C20.3854 5.07354 19.9265 4.6146 19.362 4.32698C18.7202 4 17.8802 4 16.2 4H7.8C6.11984 4 5.27976 4 4.63803 4.32698C4.07354 4.6146 3.6146 5.07354 3.32698 5.63803C3 6.27976 3 7.11984 3 8.8V17.2C3 18.8802 3 19.7202 3.32698 20.362C3.6146 20.9265 4.07354 21.3854 4.63803 21.673C5.27976 22 6.11984 22 7.8 22H12.5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M3 8H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M7 4V2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M17 4V2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M18 14L16 16L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                            <circle cx="18" cy="18" r="4" stroke="currentColor" strokeWidth="2" />
                                        </svg>
                                    </div>
                                    <h3 className="font-bold text-xl mb-2">Vendor</h3>
                                    <p className="text-center text-sm opacity-80 leading-relaxed">
                                        Manage your business, accept bookings, and grow your customer base with our powerful tools
                                    </p>
                                    {formData.role === 'vendor' && (
                                        <div className="mt-4 flex items-center text-orange-800 dark:text-orange-600">
                                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                            </svg>
                                            <span className="font-semibold">Selected</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                            {/* {Object.keys(errors).length > 0 && (
                                <div className="bg-red-100 mb-4 px-4 py-2 border border-red-400 rounded text-red-700">
                                    {Object.values(errors).map((err, idx) => (
                                        <div key={idx}>{err}</div>
                                    ))}
                                </div>
                            )} */}

                        <form className="space-y-6" onSubmit={submitForm}>
                            {/* Form Fields Grid */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                {/* Name Field */}
                                <div className="space-y-2">
                                    <label htmlFor="name" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        Full Name
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                        <input
                                            id="name"
                                            type="text"
                                            placeholder="Enter your full name"
                                            className="w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-800/20 focus:border-orange-800 transition-all duration-300 shadow-sm hover:shadow-md"
                                            value={formData.name}
                                            onChange={handleChange}
                                            required
                                        />
                                    </div>
                                    {errors.name && (
                                        <div className="flex items-center mt-2 text-red-600 dark:text-red-400">
                                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-sm font-medium">{errors.name}</span>
                                        </div>
                                    )}
                                </div>

                                {/* Email Field */}
                                <div className="space-y-2">
                                    <label htmlFor="email" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        Email Address
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                            </svg>
                                        </div>
                                        <input
                                            id="email"
                                            type="email"
                                            placeholder="Enter your email address"
                                            className="w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-800/20 focus:border-orange-800 transition-all duration-300 shadow-sm hover:shadow-md"
                                            value={formData.email}
                                            onChange={handleChange}
                                            required
                                        />
                                    </div>
                                    {errors.email && (
                                        <div className="flex items-center mt-2 text-red-600 dark:text-red-400">
                                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-sm font-medium">{errors.email}</span>
                                        </div>
                                    )}
                                </div>

                                {/* Password Field */}
                                <div className="space-y-2">
                                    <label htmlFor="password" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        Password
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                            </svg>
                                        </div>
                                        <input
                                            id="password"
                                            type="password"
                                            placeholder="Enter your password"
                                            className="w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-800/20 focus:border-orange-800 transition-all duration-300 shadow-sm hover:shadow-md"
                                            value={formData.password}
                                            onChange={handleChange}
                                            required
                                        />
                                    </div>
                                    {errors.password && (
                                        <div className="flex items-center mt-2 text-red-600 dark:text-red-400">
                                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-sm font-medium">{errors.password}</span>
                                        </div>
                                    )}
                                </div>

                                {/* Confirm Password Field */}
                                <div className="space-y-2">
                                    <label htmlFor="password_confirmation" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        Confirm Password
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <input
                                            id="password_confirmation"
                                            type="password"
                                            placeholder="Confirm your password"
                                            className="w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-800/20 focus:border-orange-800 transition-all duration-300 shadow-sm hover:shadow-md"
                                            value={formData.password_confirmation}
                                            onChange={handleChange}
                                            required
                                        />
                                    </div>
                                    {errors.password_confirmation && (
                                        <div className="flex items-center mt-2 text-red-600 dark:text-red-400">
                                            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                            </svg>
                                            <span className="text-sm font-medium">{errors.password_confirmation}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Hidden role field - value is set by the card selection */}
                            <input type="hidden" id="role" value={formData.role} />

                            {/* Vendor Fields */}
                            {showVendorFields && (
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6 bg-orange-50 dark:bg-orange-900/10 rounded-3xl border border-orange-200 dark:border-orange-800/30">
                                    <div className="lg:col-span-2 text-center mb-4">
                                        <h3 className="text-lg font-bold text-orange-800 dark:text-orange-600 mb-2">
                                            Business Information
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            Please provide your business details
                                        </p>
                                    </div>

                                    {/* Company Name Field */}
                                    <div className="space-y-2">
                                        <label htmlFor="company_name" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                            Company Name
                                        </label>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                                </svg>
                                            </div>
                                            <input
                                                id="company_name"
                                                type="text"
                                                placeholder="Enter your company name"
                                                className="w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-800/20 focus:border-orange-800 transition-all duration-300 shadow-sm hover:shadow-md"
                                                value={formData.company_name}
                                                onChange={handleChange}
                                                required={showVendorFields}
                                            />
                                        </div>
                                        {errors.company_name && (
                                            <div className="flex items-center mt-2 text-red-600 dark:text-red-400">
                                                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                                </svg>
                                                <span className="text-sm font-medium">{errors.company_name}</span>
                                            </div>
                                        )}
                                    </div>

                                    {/* Company Domain Field */}
                                    <div className="space-y-2">
                                        <label htmlFor="company_domain" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                            Company Domain
                                        </label>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                                                </svg>
                                            </div>
                                            <input
                                                id="company_domain"
                                                type="text"
                                                placeholder="Enter your domain"
                                                className="w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-800/20 focus:border-orange-800 transition-all duration-300 shadow-sm hover:shadow-md"
                                                value={formData.company_domain}
                                                onChange={handleChange}
                                                required={showVendorFields}
                                            />
                                        </div>
                                        <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800/30">
                                            <p className="text-sm text-blue-800 dark:text-blue-300">
                                                <span className="font-semibold">Your subdomain:</span><br />
                                                <span className="font-mono bg-blue-100 dark:bg-blue-800/30 px-2 py-1 rounded">
                                                    {formData.company_domain ? `${formData.company_domain}.${centralDomain}` : `yourdomain.${centralDomain}`}
                                                </span>
                                            </p>
                                        </div>
                                        {errors.company_domain && (
                                            <div className="flex items-center mt-2 text-red-600 dark:text-red-400">
                                                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                                </svg>
                                                <span className="text-sm font-medium">{errors.company_domain}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Submit Button */}
                            <button
                                type="submit"
                                className="w-full mt-8 px-6 py-4 bg-gradient-to-r from-orange-800 to-orange-900 hover:from-orange-900 hover:to-orange-800 text-white font-bold text-lg rounded-2xl shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-[1.02] focus:outline-none focus:ring-4 focus:ring-orange-800/30 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                disabled={processing}
                            >
                                {processing ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Creating Account...
                                    </>
                                ) : (
                                    <>
                                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                                        </svg>
                                        Create Account
                                    </>
                                )}
                            </button>
                        </form>

                        {/* Footer */}
                        <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 text-center">
                            <p className="text-gray-600 dark:text-gray-400 font-medium">
                                Already have an account?{' '}
                                <Link
                                    href="/auth/login"
                                    className="font-bold text-orange-800 hover:text-orange-900 dark:text-orange-600 dark:hover:text-orange-500 transition-colors duration-200 hover:underline"
                                >
                                    Sign In
                                </Link>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RegisterBoxed;

