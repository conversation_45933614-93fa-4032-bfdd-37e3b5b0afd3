import { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import { IRootState } from '@/store';
import { setPageTitle } from '@/store/themeConfigSlice';
import Swal, { SweetAlertIcon } from 'sweetalert2';

interface Props {
    branches: {
        data: Array<{
            id: number;
            name: string;
            address: string;
            phone: string;
            email: string | null;
            is_active: boolean;
            currency_symbol?: string;
            currency_text?: string;
            allow_staff: boolean;
        }>;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search: string;
        status: string;
        sort: string;
        direction: string;
    };
    current_branch_id: number;
    flash?: {
        success?: string;
        error?: string;
    };
}

const Index = ({ branches, filters, current_branch_id, flash }: Props) => {
    const dispatch = useDispatch();
    const [search, setSearch] = useState(filters.search);
    const [status, setStatus] = useState(filters.status);

    useEffect(() => {
        dispatch(setPageTitle('Branch Management'));
    });

    const showMessage = (msg = '', type: SweetAlertIcon = 'success') => {
        const toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            customClass: { container: 'toast' },
        });
        toast.fire({
            icon: type,
            title: msg,
            padding: '10px 20px',
        });
    };

    useEffect(() => {
        if (flash?.success) {
            showMessage(flash.success, 'success');
        }
        if (flash?.error) {
            showMessage(flash.error, 'error');
        }
    }, [flash]);

    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(
            '/vendor/branches',
            { search: value, status, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleStatusChange = (value: string) => {
        setStatus(value);
        router.get(
            '/vendor/branches',
            { search, status: value, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleSort = (field: string) => {
        const direction = filters.sort === field && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            '/vendor/branches',
            { search, status, sort: field, direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleDelete = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to delete this branch?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(`/vendor/branches/${id}`, {
                    onSuccess: () => {
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'Branch has been deleted.',
                            icon: 'success',
                            padding: '2em',
                            customClass: {
                                container: 'sweet-alerts'
                            }
                        });
                    },
                    onError: () => {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong while deleting the branch.',
                            icon: 'error',
                            padding: '2em',
                            customClass: {
                                container: 'sweet-alerts'
                            }
                        });
                    },
                });
            }
        });
    };

    const handleBranchActivation = (branchId: number) => {
        router.post('/vendor/branches/update-current',
            { branch_id: branchId },
            {
                preserveScroll: true,
                preserveState: true
            }
        );
    };

    const handleAllowStaffToggle = (branchId: number, newValue: boolean) => {
        router.put(`/vendor/branches/${branchId}/allow-staff`, { allow_staff: newValue }, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => showMessage('Allow staff status updated successfully', 'success'),
            onError: () => showMessage('Failed to update allow staff status', 'error'),
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Branches</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex md:flex-row flex-col md:items-center gap-5 mb-5">
                        <div className="flex flex-1 items-center gap-5">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="py-2 ltr:pr-11 rtl:pl-11 form-input peer"
                                    value={search}
                                    onChange={(e) => handleSearch(e.target.value)}
                                />
                                <button type="button" className="top-1/2 ltr:right-[11px] rtl:left-[11px] absolute peer-focus:text-primary -translate-y-1/2">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11.5" cy="11.5" r="9.5" stroke="currentColor" strokeWidth="1.5" opacity="0.5"></circle>
                                        <path d="M18.5 18.5L22 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                                    </svg>
                                </button>
                            </div>
                            <div className="flex items-center gap-5">
                                <select
                                    className="w-32 form-select"
                                    value={status}
                                    onChange={(e) => handleStatusChange(e.target.value)}
                                >
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div className="flex items-center gap-5">
                            <Link href={route('vendor.branches.working-hours.index')} className="btn btn-primary">
                                Manage Working Hours
                            </Link>
                            <Link href="/vendor/branches/create" className="btn btn-primary">
                                <svg className="ltr:mr-2 rtl:ml-2 w-5 h-5" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                                Add New Branch
                            </Link>
                            <Link href="/vendor/branches/trashed" className="btn btn-outline-warning">
                                <svg className="ltr:mr-2 rtl:ml-2 w-5 h-5" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                </svg>
                                Trashed Branches
                            </Link>
                        </div>
                    </div>

                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th onClick={() => handleSort('name')} className="cursor-pointer">
                                        Name {filters.sort === 'name' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th onClick={() => handleSort('address')} className="cursor-pointer">
                                        Address {filters.sort === 'address' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Currency</th>
                                    <th>Allow Staff</th>
                                    <th onClick={() => handleSort('is_active')} className="cursor-pointer">
                                        Status {filters.sort === 'is_active' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th>Current Branch</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {branches.data.map((branch) => (
                                    <tr key={branch.id}>
                                        <td>{branch.name}</td>
                                        <td>{branch.address}</td>
                                        <td>{branch.phone}</td>
                                        <td>{branch.email || '-'}</td>
                                        <td>{branch.currency_text || '-'} ( {branch.currency_symbol || '-'} ) </td>
                                        <td>
                                            <label className="inline-flex relative items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    className="sr-only peer"
                                                    checked={branch.allow_staff}
                                                    onChange={() => handleAllowStaffToggle(branch.id, !branch.allow_staff)}
                                                />
                                                <div className="after:top-[2px] after:left-[2px] after:absolute after:content-[''] bg-gray-200 after:bg-white peer-checked:bg-primary after:border after:border-gray-300 peer-checked:after:border-white rounded-full after:rounded-full peer-focus:ring-4 peer-focus:ring-primary/20 w-11 after:w-5 h-6 after:h-5 after:transition-all peer-checked:after:translate-x-full peer-focus:outline-none peer"></div>
                                            </label>
                                        </td>
                                        <td>
                                            <span className={`badge badge-outline-${branch.is_active ? 'success' : 'danger'}`}>
                                                {branch.is_active ? 'Active' : 'Inactive'}
                                            </span>
                                        </td>
                                        <td>
                                            <label className="inline-flex relative items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    className="sr-only peer"
                                                    checked={branch.id === current_branch_id}
                                                    onChange={() => handleBranchActivation(branch.id)}
                                                    disabled={!branch.is_active}
                                                />
                                                <div className="after:top-[2px] after:left-[2px] after:absolute after:content-[''] bg-gray-200 after:bg-white peer-checked:bg-primary after:border after:border-gray-300 peer-checked:after:border-white rounded-full after:rounded-full peer-focus:ring-4 peer-focus:ring-primary/20 w-11 after:w-5 h-6 after:h-5 after:transition-all peer-checked:after:translate-x-full peer-focus:outline-none peer"></div>
                                            </label>
                                        </td>
                                        <td className="text-center">
                                            <div className="flex justify-center items-center gap-4">
                                                <Link href={`/vendor/branches/${branch.id}/edit`} className="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </Link>
                                                <button
                                                    type="button"
                                                    className="btn btn-sm btn-outline-danger"
                                                    onClick={() => handleDelete(branch.id)}
                                                    disabled={branches.data.length === 1 || branch.id === current_branch_id}
                                                    title={
                                                        branches.data.length === 1
                                                            ? "Cannot delete the last branch"
                                                            : branch.id === current_branch_id
                                                                ? "Cannot delete the active branch"
                                                                : "Delete branch"
                                                    }
                                                >
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    <div className="mt-6">
                        <div className="flex flex-wrap justify-center items-center gap-4">
                            {branches.links.map((link, i) => (
                                <Link
                                    key={i}
                                    href={link.url || '#'}
                                    className={`px-4 py-2 text-sm font-semibold rounded-md ${link.active
                                            ? 'bg-primary text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    {link.label === '&laquo; Previous' ? 'Previous' :
                                        link.label === 'Next &raquo;' ? 'Next' :
                                            link.label}
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Index; 