import { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import Swal from 'sweetalert2';

interface Props {
    branches: {
        data: Array<{
            id: number;
            name: string;
            address: string;
            phone: string;
            email: string | null;
            is_active: boolean;
            deleted_at: string;
        }>;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search: string;
        sort: string;
        direction: string;
    };
}

const Trashed = ({ branches, filters }: Props) => {
    const dispatch = useDispatch();
    const [search, setSearch] = useState(filters.search);

    useEffect(() => {
        dispatch(setPageTitle('Trashed Branches'));
    });

    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(
            '/vendor/branches/trashed',
            { search: value, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleSort = (field: string) => {
        const direction = filters.sort === field && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            '/vendor/branches/trashed',
            { search, sort: field, direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleRestore = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to restore this branch?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, restore it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.put(`/vendor/branches/${id}/restore`, {}, {
                    onSuccess: () => {
                        Swal.fire({
                            title: 'Restored!',
                            text: 'Branch has been restored.',
                            icon: 'success',
                            padding: '2em',
                            customClass: {
                                container: 'sweet-alerts'
                            }
                        });
                    },
                    onError: () => {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong while restoring the branch.',
                            icon: 'error',
                            padding: '2em',
                            customClass: {
                                container: 'sweet-alerts'
                            }
                        });
                    },
                });
            }
        });
    };

    const handleForceDelete = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to permanently delete this branch?<br>This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(`/vendor/branches/${id}/force-delete`, {
                    onSuccess: () => {
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'Branch has been permanently deleted.',
                            icon: 'success',
                            padding: '2em',
                            customClass: {
                                container: 'sweet-alerts'
                            }
                        });
                    },
                    onError: () => {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong while deleting the branch.',
                            icon: 'error',
                            padding: '2em',
                            customClass: {
                                container: 'sweet-alerts'
                            }
                        });
                    },
                });
            }
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/branches" className="text-primary hover:underline">
                        Branches
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Trashed</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex md:items-center md:flex-row flex-col mb-5 gap-5">
                        <div className="flex items-center gap-5 flex-1">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="form-input py-2 ltr:pr-11 rtl:pl-11 peer"
                                    value={search}
                                    onChange={(e) => handleSearch(e.target.value)}
                                />
                                <button type="button" className="absolute ltr:right-[11px] rtl:left-[11px] top-1/2 -translate-y-1/2 peer-focus:text-primary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11.5" cy="11.5" r="9.5" stroke="currentColor" strokeWidth="1.5" opacity="0.5"></circle>
                                        <path d="M18.5 18.5L22 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div className="flex items-center gap-5">
                            <Link href="/vendor/branches" className="btn btn-outline-primary">
                                Back to Branches
                            </Link>
                        </div>
                    </div>

                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th onClick={() => handleSort('name')} className="cursor-pointer">
                                        Name {filters.sort === 'name' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th onClick={() => handleSort('address')} className="cursor-pointer">
                                        Address {filters.sort === 'address' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th onClick={() => handleSort('deleted_at')} className="cursor-pointer">
                                        Deleted At {filters.sort === 'deleted_at' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {branches.data.map((branch) => (
                                    <tr key={branch.id}>
                                        <td>{branch.name}</td>
                                        <td>{branch.address}</td>
                                        <td>{branch.phone}</td>
                                        <td>{branch.email || '-'}</td>
                                        <td>{new Date(branch.deleted_at).toLocaleString()}</td>
                                        <td className="text-center">
                                            <div className="flex gap-4 items-center justify-center">
                                                <button
                                                    type="button"
                                                    className="btn btn-sm btn-outline-primary"
                                                    onClick={() => handleRestore(branch.id)}
                                                >
                                                    Restore
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    <div className="mt-6">
                        <div className="flex flex-wrap items-center justify-center gap-4">
                            {branches.links.map((link, i) => (
                                <Link
                                    key={i}
                                    href={link.url || '#'}
                                    className={`px-4 py-2 text-sm font-semibold rounded-md ${
                                        link.active
                                            ? 'bg-primary text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                    }`}
                                >
                                    {link.label === '&laquo; Previous' ? 'Previous' : 
                                     link.label === 'Next &raquo;' ? 'Next' : 
                                     link.label}
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Trashed; 