<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

final class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'user_id',
        'vendor_id',
        // Vendor details
        'vendor_company_name',
        'vendor_party_code',
        'vendor_contact_person_name',
        'vendor_contact_person_number',
        'vendor_email',
        'vendor_gstin',
        'vendor_country_id',
        'vendor_state_id',
        'vendor_city_id',
        'vendor_pincode',
        'vendor_address',
        'vendor_pan_number',
        // Invoice details
        'invoice_number',
        'invoice_date',
        'invoice_type',
        'subtotal',
        'is_gst_applicable',
        'gst_amount',
        'discount',
        'grand_total',
        'remarks',
        'gst_applicable_amount',
        'payment_method',
        'without_gst_amount',
        'with_gst_amount',
        'payment_status',
    ];

    protected $casts = [
        'invoice_date'          => 'date',
        'is_gst_applicable'     => 'boolean',
        'subtotal'              => 'decimal:2',
        'gst_amount'            => 'decimal:2',
        'discount'              => 'decimal:2',
        'grand_total'           => 'decimal:2',
        'gst_applicable_amount' => 'decimal:2',
        'without_gst_amount'    => 'decimal:2',
        'with_gst_amount'       => 'decimal:2',
    ];

    protected $attributes = [
        'payment_status' => 'pending',
        'invoice_type'   => 'sales',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function vendorCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'vendor_country_id');
    }

    public function vendorState(): BelongsTo
    {
        return $this->belongsTo(State::class, 'vendor_state_id');
    }

    public function vendorCity(): BelongsTo
    {
        return $this->belongsTo(City::class, 'vendor_city_id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function termConditions(): HasMany
    {
        return $this->hasMany(InvoiceTermCondition::class);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('invoice_number', 'like', "%{$search}%")
                ->orWhere('vendor_company_name', 'like', "%{$search}%")
                ->orWhere('vendor_party_code', 'like', "%{$search}%")
                ->orWhere('vendor_gstin', 'like', "%{$search}%");
        });
    }

    public function scopeSort($query, $field, $direction)
    {
        return $query->orderBy($field, $direction);
    }
}
