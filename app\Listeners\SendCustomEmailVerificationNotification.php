<?php

namespace App\Listeners;

use App\Mail\EmailVerificationMail;
use App\Services\Notification\NotificationService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class SendCustomEmailVerificationNotification implements ShouldQueue
{
    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        $user = $event->user;

        // Only send verification email if the user hasn't verified their email yet
        if (!$user->hasVerifiedEmail()) {
            // Use defer for better user experience
            defer(function () use ($user) {
                try {
                    NotificationService::sendMail(
                        $user->email,
                        new EmailVerificationMail($user)
                    );
                } catch (\Exception $e) {
                    // Log the error but don't fail the registration
                    Log::error('Failed to send verification email: ' . $e->getMessage());
                }
            });
        }
    }
}
