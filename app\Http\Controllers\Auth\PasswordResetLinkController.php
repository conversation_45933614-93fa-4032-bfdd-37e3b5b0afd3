<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\PasswordResetMail;
use App\Models\User;
use App\Services\Notification\NotificationService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

final class PasswordResetLinkController extends Controller
{
    /**
     * Show the password reset link request page.
     */
    public function create(Request $request): Response
    {
        return Inertia::render('Authentication/ForgotPassword', [
            'status' => $request->session()->get('status'),
            'layout'           => 'blank',
        ]);
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $email = $request->email;
        $user = User::where('email', $email)->first();

        if ($user) {
            // Generate password reset token
            $token = Str::random(64);

            // Store the token in password_reset_tokens table
            DB::table('password_reset_tokens')->updateOrInsert(
                ['email' => $email],
                [
                    'email' => $email,
                    'token' => Hash::make($token),
                    'created_at' => now()
                ]
            );

            // Send email using defer for better user experience
            defer(function () use ($user, $token) {
                try {
                    NotificationService::sendMail(
                        $user->email,
                        new PasswordResetMail($user, $token)
                    );
                } catch (\Exception $e) {
                    // Log the error but don't fail the request
                    Log::error('Failed to send password reset email: ' . $e->getMessage());
                }
            });
        }

        // Always return the same message for security (don't reveal if email exists)
        return back()->with('status', __('A reset link will be sent if the account exists.'));
    }
}
