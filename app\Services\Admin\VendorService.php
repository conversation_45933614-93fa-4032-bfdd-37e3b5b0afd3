<?php

declare(strict_types=1);

namespace App\Services\Admin;

use App\Models\User;
use App\Models\VendorSubscription;

class VendorService
{
    public function list()
    {
        return User::whereHas('roles', function ($q) {
            $q->where('name', 'vendor');
        })->get();
    }

    public function find($id): ?User
    {
        return User::findOrFail($id);
    }

    public function update($id, array $data): bool
    {
        $vendor = User::findOrFail($id);

        return $vendor->update($data);
    }

    public function delete($id): bool
    {
        $vendor = User::findOrFail($id);

        return $vendor->delete();
    }

    public function subscriptions($vendorId)
    {
        return VendorSubscription::with('subscriptionPlan')
            ->where('user_id', $vendorId)
            ->orderByDesc('created_at')
            ->get();
    }

    public function accessPortal($vendorId)
    {
        $vendor        = User::findOrFail($vendorId);
        $centralDomain = config('services.central_domain.url');
        $protocol      = request()->isSecure() ? 'https' : 'http';
        $url           = $protocol.'://'.$vendor->company_domain.'.'.$centralDomain.'/vendor/dashboard';

        return $url;
    }
}
