<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\Admin\VendorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class VendorController extends Controller
{
    private VendorService $service;

    public function __construct(VendorService $service)
    {
        $this->service = $service;
    }

    public function index()
    {
        $vendors = $this->service->list();

        return Inertia::render('Admin/Vendors/Index', [
            'vendors' => $vendors,
        ]);
    }

    public function show($id)
    {
        $vendor        = $this->service->find($id);
        $subscriptions = $this->service->subscriptions($id);

        return Inertia::render('Admin/Vendors/Show', [
            'vendor'        => $vendor,
            'subscriptions' => $subscriptions,
        ]);
    }

    public function edit($id)
    {
        $vendor = $this->service->find($id);

        return Inertia::render('Admin/Vendors/Edit', [
            'vendor' => $vendor,
        ]);
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name'  => 'required|string|max:255',
            'email' => 'required|email',
            // Add more fields as needed
        ]);
        $this->service->update($id, $validated);

        return redirect()->route('siteadmin.vendors.index')->with('success', 'Vendor updated successfully.');
    }

    public function destroy($id)
    {
        $this->service->delete($id);

        return redirect()->route('siteadmin.vendors.index')->with('success', 'Vendor deleted successfully.');
    }

    public function accessPortal($id)
    {
        $adminID          = Auth::id();
        $redirectUrl      = $this->service->accessPortal($id);
        $user             = User::findOrFail($id);
        $section_title    = 'Redirecting to your dashboard';
        $section_subtitle = 'Please wait while we redirect you to your dashboard...';
        Auth::login($user);
        $redirectData = [
            'admin_user_id'    => $adminID,
            'redirect_url'     => $redirectUrl,
            'section_title'    => $section_title,
            'section_subtitle' => $section_subtitle,
        ];
        session()->flash('user_redirect_data', $redirectData);

        return redirect()->route('vendor.redirect');
    }
}
