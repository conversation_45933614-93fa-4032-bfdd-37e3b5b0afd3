<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendor_subscriptions', function (Blueprint $table) {
            // Drop the foreign key on user_id first
            $table->dropForeign(['user_id']); // or use the exact constraint name if needed

            // Then drop the unique index
            $table->dropUnique('unique_active_subscription');
        });

        // Re-add the foreign key (optional)
        Schema::table('vendor_subscriptions', function (Blueprint $table) {
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendor_subscriptions', function (Blueprint $table) {
            // Re-add the unique index
            $table->unique(['user_id', 'status'], 'unique_active_subscription');

            // Drop and re-add user_id foreign key again (if needed for rollback)
            $table->dropForeign(['user_id']);
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }
};
