import React, { useEffect, useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import axios from 'axios';
import ImageUploading, { ImageListType } from 'react-images-uploading';
import Swal from 'sweetalert2';

interface Media {
    id: number;
    image: string;
    type: string;
    status: boolean;
}

const MAX_NUMBER = 10;
const MAX_SIZE_MB = 3;
const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;

const Gallery = () => {
    const { flash = {} } = usePage<{ flash?: { error?: string; success?: string } }>().props;
    const [images, setImages] = useState<Media[]>([]);
    const [loading, setLoading] = useState(true);
    const [uploading, setUploading] = useState(false);
    const [uploadError, setUploadError] = useState<string | null>(null);
    const [uploadErrors, setUploadErrors] = useState<string[]>([]);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);

    useEffect(() => {
        fetchMedia();
    }, []);

    // Show SweetAlert for flash error/success
    useEffect(() => {
        if (flash.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash.error, flash.success]);

    useEffect(() => {
        if (successMessage || uploadErrors.length > 0) {
            const timer = setTimeout(() => {
                setSuccessMessage(null);
                setUploadErrors([]);
            }, 2000);
            return () => clearTimeout(timer);
        }
    }, [successMessage, uploadErrors]);

    const fetchMedia = async () => {
        setLoading(true);
        try {
            const response = await axios.get('/vendor/media');
            setImages(response.data.filter((media: Media) => media.type === 'gallery'));
        } catch (error) {
            console.error('Error fetching media:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleUpload = async (imageList: ImageListType) => {
        setUploadError(null);
        setUploadErrors([]);
        setSuccessMessage(null);
        if (!imageList.length) return;
        setUploading(true);
        let errors: string[] = [];
        let successCount = 0;
        for (let i = 0; i < imageList.length; i++) {
            const img = imageList[i];
            if (!img.file) continue;
            if (img.file.size > MAX_SIZE_BYTES) {
                errors.push(`Image "${img.file.name}" is too large (max ${MAX_SIZE_MB}MB).`);
                continue;
            }
            if (!img.file.type.startsWith('image/')) {
                errors.push(`File "${img.file.name}" is not an image.`);
                continue;
            }
            const formData = new FormData();
            formData.append('image', img.file);
            formData.append('type', 'gallery');
            formData.append('status', '1');
            try {
                await axios.post('/vendor/media', formData, {
                    headers: { 'Content-Type': 'multipart/form-data' },
                });
                successCount++;
            } catch (e: any) {
                errors.push(`Failed to upload "${img.file.name}": ${e?.response?.data?.errors?.image?.[0] || 'Upload failed'}`);
            }
        }
        setUploading(false);
        if (errors.length) setUploadErrors(errors);
        if (successCount > 0) {
            setSuccessMessage(`${successCount} image${successCount > 1 ? 's' : ''} uploaded successfully.`);
            fetchMedia();
        }
    };

    const handleDelete = async (id: number) => {
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: 'Delete this image? This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        });
        if (result.isConfirmed) {
            await axios.delete(`/vendor/media/${id}`);
            fetchMedia();
        }
    };

    const handleStatusToggle = async (id: number, status: boolean) => {
        await axios.put(`/vendor/media/${id}`, { status: !status });
        fetchMedia();
    };

    return (
        <div>
            <div className="panel">
                <div className="mb-5">
                    <h5 className="font-semibold text-lg dark:text-white-light">Gallery Management</h5>
                </div>
                <ImageUploading
                    multiple
                    value={[]}
                    onChange={handleUpload}
                    maxNumber={MAX_NUMBER}
                    dataURLKey="data_url"
                >
                    {({ onImageUpload, isDragging, dragProps }) => (
                        <div className="mb-4">
                            <button
                                type="button"
                                className={`btn btn-primary ${uploading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                onClick={onImageUpload}
                                disabled={uploading}
                                {...dragProps}
                            >
                                {uploading ? 'Uploading...' : `Upload Images (max ${MAX_NUMBER})`}
                            </button>
                            <div className="text-xs text-gray-500 mt-2">Max size per image: {MAX_SIZE_MB}MB</div>
                            {uploadError && <div className="text-danger mt-2">{uploadError}</div>}
                            {uploadErrors.length > 0 && (
                                <ul className="text-danger mt-2">
                                    {uploadErrors.map((err, idx) => <li key={idx}>{err}</li>)}
                                </ul>
                            )}
                            {successMessage && <div className="text-success mt-2">{successMessage}</div>}
                        </div>
                    )}
                </ImageUploading>
                {loading ? (
                    <div className="flex justify-center items-center h-40">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                        {images.map((image) => (
                            <div key={image.id} className="relative group">
                                <img
                                    src={`/${image.image}`}
                                    alt="Gallery"
                                    className="w-full h-48 object-cover rounded-lg"
                                />
                                {/* Status dot */}
                                <span className={`absolute top-2 left-2 w-4 h-4 rounded-full border-2 border-white ${image.status ? 'bg-green-500' : 'bg-red-500'}`}
                                    title={image.status ? 'Active' : 'Inactive'}
                                ></span>
                                {/* Hover overlay for actions */}
                                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center space-x-4">
                                    <button
                                        onClick={() => handleStatusToggle(image.id, image.status)}
                                        className={`p-2 rounded-full ${image.status ? 'bg-green-500' : 'bg-gray-500'} text-white hover:bg-opacity-80`}
                                    >
                                        {image.status ? (
                                            <span>Deactivate</span>
                                        ) : (
                                            <span>Activate</span>
                                        )}
                                    </button>
                                    <button
                                        onClick={() => handleDelete(image.id)}
                                        className="p-2 rounded-full bg-red-500 text-white hover:bg-opacity-80"
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default Gallery; 