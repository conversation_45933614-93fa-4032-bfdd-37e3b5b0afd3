import React, { useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@store/themeConfigSlice';

interface Appointment {
    id: number;
    appointment_date: string;
    appointment_time: string;
    ticket_number: string;
    status: string;
    notes: string;
    services: Array<{
        id: number;
        name: string;
        price: number;
    }>;
}

interface Customer {
    id: number;
    name: string;
    email: string;
    phone: string;
    gender: string;
    company_name: string;
    address: string;
    is_active: boolean;
    created_at: string;
    appointments: Appointment[];
    appointments_count: number;
}

interface Stats {
    total_appointments: number;
    completed_appointments: number;
    pending_appointments: number;
    cancelled_appointments: number;
    in_progress_appointments: number;
    first_booking: string | null;
    last_booking: string | null;
}

interface Props {
    customer: Customer;
    stats: Stats;
}

const Show: React.FC<Props> = ({ customer, stats }) => {
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch(setPageTitle(`Customer - ${customer.name}`));
    }, [dispatch, customer.name]);

    const getStatusBadgeClass = (status: string) => {
        switch (status) {
            case 'completed':
                return 'badge badge-outline-success';
            case 'pending':
                return 'badge badge-outline-warning';
            case 'cancelled':
                return 'badge badge-outline-danger';
            case 'in_progress':
                return 'badge badge-outline-info';
            default:
                return 'badge badge-outline-secondary';
        }
    };

    const getGenderBadgeClass = (gender: string) => {
        return gender === 'male'
            ? 'badge badge-outline-info'
            : 'badge badge-outline-warning';
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/manage-customer" className="text-primary hover:underline">
                        Customers
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={`/vendor/manage-customer/show/${customer.id}`} className="text-primary hover:underline">
                        {customer.name}
                    </Link>
                </li>
            </ul>

            <div className="pt-5">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Customer Information */}
                    <div className="lg:col-span-2">
                        <div className="panel">
                            <div className="flex items-center justify-between mb-5">
                                <h5 className="font-semibold text-lg dark:text-white-light">Customer Information</h5>
                                <Link
                                    href={`/vendor/manage-customer/show/${customer.id}/booking-history`}
                                    className="btn btn-primary"
                                >
                                    View Booking History
                                </Link>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="form-label">Name</label>
                                    <p className="text-lg font-semibold">{customer.name}</p>
                                </div>

                                <div>
                                    <label className="form-label">Email</label>
                                    <p className="text-lg">{customer.email}</p>
                                </div>

                                <div>
                                    <label className="form-label">Phone</label>
                                    <p className="text-lg">{customer.phone || 'N/A'}</p>
                                </div>

                                <div>
                                    <label className="form-label">Gender</label>
                                    <p>
                                        <span className={getGenderBadgeClass(customer.gender)}>
                                            {customer.gender || 'N/A'}
                                        </span>
                                    </p>
                                </div>

                                <div>
                                    <label className="form-label">Company</label>
                                    <p className="text-lg">{customer.company_name || 'N/A'}</p>
                                </div>

                                <div>
                                    <label className="form-label">Status</label>
                                    <p>
                                        <span className={customer.is_active ? 'badge badge-outline-success' : 'badge badge-outline-danger'}>
                                            {customer.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </p>
                                </div>

                                <div className="md:col-span-2">
                                    <label className="form-label">Address</label>
                                    <p className="text-lg">{customer.address || 'N/A'}</p>
                                </div>

                                <div>
                                    <label className="form-label">Member Since</label>
                                    <p className="text-lg">{new Date(customer.created_at).toLocaleDateString()}</p>
                                </div>

                                <div>
                                    <label className="form-label">Total Bookings</label>
                                    <p className="text-lg font-semibold text-primary">{customer.appointments_count}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Statistics */}
                    <div className="lg:col-span-1">
                        <div className="panel">
                            <h5 className="font-semibold text-lg dark:text-white-light mb-5">Booking Statistics</h5>

                            <div className="space-y-4">
                                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-dark rounded">
                                    <span>Total Bookings</span>
                                    <span className="font-semibold text-primary">{stats.total_appointments}</span>
                                </div>

                                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-dark rounded">
                                    <span>Completed</span>
                                    <span className="font-semibold text-success">{stats.completed_appointments}</span>
                                </div>

                                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-dark rounded">
                                    <span>Pending</span>
                                    <span className="font-semibold text-warning">{stats.pending_appointments}</span>
                                </div>

                                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-dark rounded">
                                    <span>In Progress</span>
                                    <span className="font-semibold text-info">{stats.in_progress_appointments}</span>
                                </div>

                                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-dark rounded">
                                    <span>Cancelled</span>
                                    <span className="font-semibold text-danger">{stats.cancelled_appointments}</span>
                                </div>

                                {stats.first_booking && (
                                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-dark rounded">
                                        <span>First Booking</span>
                                        <span className="font-semibold">{new Date(stats.first_booking).toLocaleDateString()}</span>
                                    </div>
                                )}

                                {stats.last_booking && (
                                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-dark rounded">
                                        <span>Last Booking</span>
                                        <span className="font-semibold">{new Date(stats.last_booking).toLocaleDateString()}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Recent Appointments */}
                <div className="panel mt-6">
                    <h5 className="font-semibold text-lg dark:text-white-light mb-5">Recent Appointments</h5>

                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Ticket</th>
                                    <th>Services</th>
                                    <th>Status</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {customer.appointments.slice(0, 5).map((appointment) => (
                                    <tr key={appointment.id}>
                                        <td>{new Date(appointment.appointment_date).toLocaleDateString()}</td>
                                        <td>{appointment.appointment_time}</td>
                                        <td>
                                            <span className="font-mono text-sm">{appointment.ticket_number}</span>
                                        </td>
                                        <td>
                                            <div className="space-y-1">
                                                {appointment.services.map((service) => (
                                                    <div key={service.id} className="text-sm">
                                                        {service.name} - ${service.price}
                                                    </div>
                                                ))}
                                            </div>
                                        </td>
                                        <td>
                                            <span className={getStatusBadgeClass(appointment.status)}>
                                                {appointment.status.replace('_', ' ')}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="text-sm text-gray-500">
                                                {appointment.notes || 'No notes'}
                                            </span>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {customer.appointments.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                            No appointments found for this customer.
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Show;
