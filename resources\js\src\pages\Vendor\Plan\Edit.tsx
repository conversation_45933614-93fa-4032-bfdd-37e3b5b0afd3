import React, { useEffect, useState } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import { useDispatch } from 'react-redux';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
    pivot?: {
        allowed_count: number;
    };
}

interface Plan {
    id: number;
    name: string;
    description: string;
    price: number;
    validity_days: number;
    status: string;
    services: Service[];
}

interface Props {
    plan: Plan;
    services: Service[];
}

const Edit = ({ plan, services }: Props) => {
    const dispatch = useDispatch();
    const { props } = usePage() as { props: any };
    const auth = props.auth || {};
    const branch = auth.user?.branch || {};
    const currency_symbol = branch.currency_symbol || '₹';
    const currency_text = branch.currency_text || 'INR';
    const { errors } = props as any;
    
    const [formData, setFormData] = useState({
        name: plan.name,
        description: plan.description || '',
        price: plan.price.toString(),
        validity_days: plan.validity_days.toString(),
        status: plan.status,
        services: plan.services.map((service) => ({
            service_id: service.id,
            allowed_count: service.pivot?.allowed_count || 0,
        })),
    });

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleServiceChange = (serviceId: number, allowedCount: number) => {

        setFormData((prev) => {
            const existingServiceIndex = prev.services.findIndex((s) => s.service_id === serviceId);
            const updatedServices = [...prev.services];
            if(!allowedCount){
                allowedCount = 0;
            }
            if (existingServiceIndex !== -1) {
                if (allowedCount === 0) {
                    updatedServices.splice(existingServiceIndex, 1);
                } else {
                    updatedServices[existingServiceIndex].allowed_count = allowedCount;
                }
            } else if (allowedCount > 0) {
                updatedServices.push({ service_id: serviceId, allowed_count: allowedCount });
            }

            console.log(allowedCount);

            return {
                ...prev,
                services: updatedServices,
            };
        });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        router.put(`/vendor/plans/${plan.id}`, formData);
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/plans" className="text-primary hover:underline">
                        Plans
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    {errors.error && (
                        <div className="bg-red-100 border border-red-200 text-red-600 px-4 py-3 rounded mb-4">
                            {errors.error}
                        </div>
                    )}
                    <form onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 gap-4 mb-5">
                            <div>
                                <label htmlFor="name">Name</label>
                                <input
                                    id="name"
                                    type="text"
                                    name="name"
                                    className={`form-input ${errors.name ? 'border-red-500' : ''}`}
                                    value={formData.name}
                                    onChange={handleInputChange}
                                />
                                {errors.name && <div className="text-red-500 mt-1">{errors.name}</div>}
                            </div>
                            <div>
                                <label htmlFor="description">Description</label>
                                <textarea
                                    id="description"
                                    name="description"
                                    className={`form-textarea ${errors.description ? 'border-red-500' : ''}`}
                                    value={formData.description}
                                    onChange={handleInputChange}
                                    rows={3}
                                />
                                {errors.description && <div className="text-red-500 mt-1">{errors.description}</div>}
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="price">Price ({currency_symbol} {currency_text})</label>
                                    <input
                                        id="price"
                                        type="number"
                                        name="price"
                                        className={`form-input ${errors.price ? 'border-red-500' : ''}`}
                                        value={formData.price}
                                        onChange={handleInputChange}
                                        min="0"
                                        step="0.01"
                                    />
                                    {errors.price && <div className="text-red-500 mt-1">{errors.price}</div>}
                                </div>
                                <div>
                                    <label htmlFor="validity_days">Validity (Days)</label>
                                    <input
                                        id="validity_days"
                                        type="number"
                                        name="validity_days"
                                        className={`form-input ${errors.validity_days ? 'border-red-500' : ''}`}
                                        value={formData.validity_days}
                                        onChange={handleInputChange}
                                        min="1"
                                    />
                                    {errors.validity_days && <div className="text-red-500 mt-1">{errors.validity_days}</div>}
                                </div>
                            </div>
                            <div>
                                <label htmlFor="status">Status</label>
                                <select
                                    id="status"
                                    name="status"
                                    className={`form-select ${errors.status ? 'border-red-500' : ''}`}
                                    value={formData.status}
                                    onChange={handleInputChange}
                                >
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                                {errors.status && <div className="text-red-500 mt-1">{errors.status}</div>}
                            </div>
                        </div>

                        <div className="mb-5">
                            <h5 className="font-semibold text-lg mb-4">Services</h5>
                            {errors.services && <div className="text-red-500 mb-2">{errors.services}</div>}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {services.map((service) => (
                                    <div key={service.id} className="border rounded-md p-4">
                                        <div className="flex justify-between items-center mb-2">
                                            <span className="font-semibold">{service.name}</span>
                                            <span className="text-primary">{currency_symbol}{service.price}</span>
                                        </div>
                                        <div className="text-sm text-gray-500 mb-2">
                                            Duration: {service.duration_minutes} minutes
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <label htmlFor={`service-${service.id}`} className="text-sm">
                                                Allowed Count:
                                            </label>
                                            <input
                                                id={`service-${service.id}`}
                                                type="number"
                                                className={`form-input w-20 ${
                                                    errors[`services.${service.id}.allowed_count`] ? 'border-red-500' : ''
                                                }`}
                                                min="0"
                                                value={
                                                    formData.services.find((s) => s.service_id === service.id)
                                                        ?.allowed_count || 0
                                                }
                                                onChange={(e) =>
                                                    handleServiceChange(service.id, parseInt(e.target.value))
                                                }
                                            />
                                            {errors[`services.${service.id}.allowed_count`] && (
                                                <div className="text-red-500 text-sm">
                                                    {errors[`services.${service.id}.allowed_count`]}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="flex justify-end gap-4">
                            <Link href="/vendor/plans" className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary">
                                Update Plan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Edit; 