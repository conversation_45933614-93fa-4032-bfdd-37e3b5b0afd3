<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\UserDataService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class CustomerController extends Controller
{
    public function __construct(private readonly UserDataService $userDataService) {}

    public function index(Request $request)
    {

        $branchId = Auth::user()->current_branch_id;

        $filters = [
            'search'    => $request->get('search', ''),
            'status'    => $request->get('status', ''),
            'gender'    => $request->get('gender', ''),
            'sort'      => $request->get('sort', 'id'),
            'direction' => $request->get('direction', 'desc'),
        ];

        $customers = $this->userDataService->getCustomersWithFilters($branchId, $filters);

        return Inertia::render('Vendor/Customer/Index', [
            'customers' => $customers,
            'filters'   => $filters,
        ]);
    }

    public function show($customerId, Request $request)
    {
        $branchId = Auth::user()->current_branch_id;

        $customer = $this->userDataService->getCustomerDetails($customerId, $branchId);

        if (! $customer) {
            abort(404, 'Customer not found');
        }

        $stats = $this->userDataService->getCustomerStats($customerId, $branchId);

        return Inertia::render('Vendor/Customer/Show', [
            'customer' => $customer,
            'stats'    => $stats,
        ]);
    }

    public function bookingHistory($customerId, Request $request)
    {
        $branchId = Auth::user()->current_branch_id;

        $customer = User::findOrFail($customerId);

        $filters = [
            'search'    => $request->get('search', ''),
            'status'    => $request->get('status', ''),
            'date_from' => $request->get('date_from', ''),
            'date_to'   => $request->get('date_to', ''),
        ];

        $appointments = $this->userDataService->getCustomerBookingHistory($customerId, $branchId, $filters);

        return Inertia::render('Vendor/Customer/BookingHistory', [
            'customer'     => $customer,
            'appointments' => $appointments,
            'filters'      => $filters,
        ]);
    }

    public function create()
    {
        return Inertia::render('Vendor/Customer/AddCustomer');
    }

    public function store(Request $request)
    {
        $branchId = Auth::user()->current_branch_id;
        $tenantId = Auth::user()->tenant_id;

        $validated = $request->validate([
            'name'    => 'required|string|max:255',
            'email'   => 'required|email|max:255',
            'phone'   => 'required|string|max:20',
            'gender'  => 'required|in:male,female',
            'address' => 'nullable|string|max:255',
        ]);

        [$user, $message] = $this->userDataService->createOrUpdateCustomer($validated, $branchId);

        return redirect()->back()->with('success', $message);
    }
}
