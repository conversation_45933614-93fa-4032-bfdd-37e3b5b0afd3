<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BranchMedia extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'branch_media';

    protected $fillable = [
        'branch_id',
        'image',
        'type', // 'gallery' or 'slider'
        'status',
    ];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }
}
