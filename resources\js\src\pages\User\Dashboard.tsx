import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Link } from '@inertiajs/react';
import { setPageTitle } from '@store/themeConfigSlice';


interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface AppoinmentPlanUsages{
    name:string;
    service_name:string;
    service_id:number;
}

interface AppoinmentServices{
    appointment_service:string;
    appointment_service_price:number;
    appointment_service_service_id:number;
    appointment_service_notes:string;
}

interface Appointment {
    id: number;
    branch_name: string;
    currency_symbol: string;
    currency_text: string;
    staff_json?: string;
    date: string;
    time: string;
    status: string;
    total_paid: number;
    ticket_number: string;
    appoinmentUsedPlan:AppoinmentPlanUsages[];
    appointmentServices:AppoinmentServices[];
}

interface PlanHistory {
    id: number;
    name: string;
    price: number;
    validity_days: number;
    status: string;
    purchased_at: string;
    currency_symbol: string;
    currency_text: string;
    staff_json: string,
    expires_at: string;
    planServiceUsages: Array<{
        id: number;
        service: {
            id: number;
            name: string;
        };
        remaining_count: number;
        used_count: number;
    }>;
}

interface ActivePlan {
    id: number;
    name: string;
    price: number;
    validity_days: number;
    expires_at: string;
}

interface Props {
    totalAppointments: number;
    totalPaid: number;
    appointmentHistory: Appointment[];
    todaysAppointments: Appointment[];
    incomingAppointments: Appointment[];
    planHistory: PlanHistory[];
    activePlan: ActivePlan | null;
}

const Dashboard = ({
    totalAppointments,
    totalPaid,
    appointmentHistory,
    todaysAppointments,
    incomingAppointments,
    planHistory,
    activePlan
}: Props) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('User Dashboard'));
    }, [dispatch]);

    const formatStatus = (status: string) => {
        return status.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    };

    const formatTimeToAMPM = (time: string) => {
        if (!time) return '';
        const [hourStr, minuteStr] = time.split(':');
        let hour = parseInt(hourStr, 10);
        const minute = minuteStr;
        const ampm = hour >= 12 ? 'PM' : 'AM';
        hour = hour % 12 || 12;
        return `${hour}:${minute} ${ampm}`;
    };

    return (
        <div className="p-2 md:p-6">
            <ul className="flex space-x-2 rtl:space-x-reverse mb-4">
                <li>
                    <Link href="/user/dashboard" className="text-primary hover:underline font-semibold">
                        Dashboard
                    </Link>
                </li>
            </ul>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-gradient-to-r from-blue-500 to-blue-400 dark:from-blue-800 dark:to-blue-600 rounded-xl shadow p-5 flex items-center">
                    <div className="bg-white bg-opacity-20 dark:bg-black dark:bg-opacity-20 rounded-full p-3 mr-4">
                        <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M8 17l4 4 4-4m-4-5v9"/></svg>
                    </div>
                    <div>
                        <div className="text-white text-sm font-medium">Total Appointments</div>
                        <div className="text-2xl font-bold text-white">{totalAppointments}</div>
                    </div>
                </div>
                <div className="bg-gradient-to-r from-green-500 to-green-400 dark:from-green-800 dark:to-green-600 rounded-xl shadow p-5 flex items-center">
                    <div className="bg-white bg-opacity-20 dark:bg-black dark:bg-opacity-20 rounded-full p-3 mr-4">
                        <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 0V4m0 16v-4"/></svg>
                    </div>
                    <div>
                        <div className="text-white text-sm font-medium">Total Paid</div>
                        <div className="text-2xl font-bold text-white">₹{Number(totalPaid).toFixed(2)}</div>
                    </div>
                </div>
                <div className="bg-gradient-to-r from-fuchsia-500 to-fuchsia-400 dark:from-fuchsia-800 dark:to-fuchsia-600 rounded-xl shadow p-5 flex items-center">
                    <div className="bg-white bg-opacity-20 dark:bg-black dark:bg-opacity-20 rounded-full p-3 mr-4">
                        <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M5 13l4 4L19 7"/></svg>
                    </div>
                    <div>
                        <div className="text-white text-sm font-medium">Active Plan</div>
                        <div className="text-lg font-bold text-white">{activePlan ? activePlan.name : 'No Active Plan'}</div>
                        {activePlan && (
                            <div className="text-xs text-white mt-1">Expires: {activePlan.expires_at}</div>
                        )}
                    </div>
                </div>
                <div className="bg-gradient-to-r from-cyan-500 to-cyan-400 dark:from-cyan-800 dark:to-cyan-600 rounded-xl shadow p-5 flex items-center">
                    <div className="bg-white bg-opacity-20 dark:bg-black dark:bg-opacity-20 rounded-full p-3 mr-4">
                        <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/></svg>
                    </div>
                    <div>
                        <div className="text-white text-sm font-medium">Today's Appointments</div>
                        <div className="text-2xl font-bold text-white">{todaysAppointments.length}</div>
                    </div>
                </div>
            </div>

            {/* Appointments Section */}
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-6 mb-8">

            <div className="bg-white dark:bg-gray-900 rounded-xl shadow p-6">
                    <h5 className="font-semibold text-lg mb-4 text-gray-800 dark:text-white">Incoming Appointments</h5>
                    <div className="overflow-x-auto">
                        <table className="min-w-full text-sm">
                            <thead>
                                <tr className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-200">
                                    <th className="p-2">Ticket</th>
                                    <th className="p-2">Date/Time</th>
                                    <th className="p-2">Branch</th>
                                    <th className="p-2">Price</th>
                                    <th className="p-2">Detail</th>
                                </tr>
                            </thead>
                            <tbody>
                                {incomingAppointments.length === 0 && (
                                    <tr><td colSpan={6} className="text-center p-4 text-gray-400 dark:text-gray-500">No incoming appointments.</td></tr>
                                )}
                                {incomingAppointments.map((a) => (
                                    <tr key={a.id} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">
                                        <td className="p-2 text-gray-700 dark:text-gray-200">{a.ticket_number}</td>
                                        <td className="p-2 text-gray-700 dark:text-gray-200">{a.date} {formatTimeToAMPM(a.time)}</td>
                                        <td className="p-2 text-gray-700 dark:text-gray-200">
                                            <div>{a.branch_name}</div>
                                            {/* Staff info */}
                                            {a.staff_json && (() => {
                                                let staff: Staff | null = null;
                                                try { staff = JSON.parse(a.staff_json); } catch {}
                                                return staff ? (
                                                    <div className="mt-1 text-xs text-blue-700 font-semibold">
                                                        Staff: {staff.name} <br/>
                                                        <span className="text-gray-500">{staff.email}</span> <br/>
                                                        <span className="text-gray-500">{staff.phone}</span>
                                                    </div>
                                                ) : null;
                                            })()}



                                        </td>
                                        <td className="p-2 text-gray-700 dark:text-gray-200">

                                             {/* Calculate discount and final total */}
                                             {(() => {
                                                const matchedServices = a.appoinmentUsedPlan.map(p => p.service_id);
                                                const discountTotal = a.appointmentServices
                                                    .filter(s => matchedServices.includes(s.appointment_service_service_id))
                                                    .reduce((sum, s) => sum + Number(s.appointment_service_price || 0), 0);

                                                const finalTotal = Number(a.total_paid || 0) - discountTotal;

                                                if (discountTotal > 0) {
                                                    return (
                                                        <div className="text-sm text-gray-700 dark:text-gray-200 mt-2 space-y-1">
                                                            <div>Total: {a.currency_symbol}{Number(a.total_paid).toFixed(2)}</div>
                                                            <div>Discount: {a.currency_symbol}{discountTotal.toFixed(2)}</div>
                                                            <div className="font-semibold">Final: {a.currency_symbol}{finalTotal.toFixed(2)}</div>
                                                        </div>
                                                    );
                                                } else {
                                                    // No discount, show only total_paid
                                                    return (
                                                        <div className="text-sm text-gray-700 dark:text-gray-200 mt-2">
                                                            {a.currency_symbol}{Number(a.total_paid).toFixed(2)}
                                                        </div>
                                                    );
                                                }
                                            })()}

                                            {/* {a.currency_symbol}{Number(a.total_paid).toFixed(2)} */}

                                            </td>

                                        <td>
                                                <div>
                                                    {a.appoinmentUsedPlan && a.appoinmentUsedPlan.length > 0 && (() => {
                                                        // Group services by plan name
                                                        const groupedPlans: { [key: string]: string[] } = {};
                                                        a.appoinmentUsedPlan.forEach((item: any) => {
                                                            if (!groupedPlans[item.name]) {
                                                                groupedPlans[item.name] = [];
                                                            }
                                                            groupedPlans[item.name].push(item.service_name);
                                                        });

                                                        return (
                                                            <div className='ml-2 badge badge-outline-success text-left'>
                                                                {Object.entries(groupedPlans).map(([planName, serviceNames], idx) => (
                                                                    <div key={idx} className="mb-1">
                                                                        <strong>{planName}</strong>
                                                                        <ul className="ml-2">
                                                                            {serviceNames.map((service, i) => (
                                                                                <li key={i}>{service}</li>
                                                                            ))}
                                                                        </ul>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        );
                                                    })()}
                                                </div>

                                            <div className='my-2'>

                                                {a.appointmentServices.length > 0 && (
                                                    <div className='ml-2 badge badge-outline-info'>
                                                        {a.appointmentServices.map((appoinmentService) => (

                                                            <>
                                                                <div><span>{appoinmentService.appointment_service} : {a.currency_symbol}{appoinmentService.appointment_service_price}</span></div>

                                                                {appoinmentService.appointment_service_notes && (
                                                                    <div><span>{appoinmentService.appointment_service_notes}</span></div>
                                                                )}
                                                            </>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>



                                            <div className='my-2'><span className={`px-2 py-1 rounded text-xs font-semibold ${a.status === 'completed' ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' : a.status === 'pending' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'}`}>{formatStatus(a.status)}</span></div>

                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div className="bg-white dark:bg-gray-900 rounded-xl shadow p-6">
                    <h5 className="font-semibold text-lg mb-4 text-gray-800 dark:text-white">Appointment History</h5>
                    <div className="overflow-x-auto">
                        <table className="min-w-full text-sm">
                            <thead>
                                <tr className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-200">
                                    <th className="p-2">Ticket</th>
                                    <th className="p-2">Date/Time</th>
                                    <th className="p-2">Branch</th>
                                    <th className="p-2">Price</th>
                                    <th className="p-2">Detail</th>
                                </tr>
                            </thead>
                            <tbody>
                                {appointmentHistory.length === 0 && (
                                    <tr><td colSpan={6} className="text-center p-4 text-gray-400 dark:text-gray-500">No appointment history found.</td></tr>
                                )}
                                {appointmentHistory.map((a) => (
                                    <tr key={a.id} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">
                                        <td className="p-2 text-gray-700 dark:text-gray-200">{a.ticket_number}</td>
                                        <td className="p-2 text-gray-700 dark:text-gray-200">{a.date} {formatTimeToAMPM(a.time)}</td>
                                        <td className="p-2 text-gray-700 dark:text-gray-200">
                                            <div>{a.branch_name}</div>
                                            {/* Staff info */}
                                            {a.staff_json && (() => {
                                                let staff: Staff | null = null;
                                                try { staff = JSON.parse(a.staff_json); } catch {}
                                                return staff ? (
                                                    <div className="mt-1 text-xs text-blue-700 font-semibold">
                                                        Staff: {staff.name} <br/>
                                                        <span className="text-gray-500">{staff.email}</span> <br/>
                                                        <span className="text-gray-500">{staff.phone}</span>
                                                    </div>
                                                ) : null;
                                            })()}



                                        </td>
                                        <td className="p-2 text-gray-700 dark:text-gray-200">

                                            {/* Calculate discount and final total */}
                                            {(() => {
                                                const matchedServices = a.appoinmentUsedPlan.map(p => p.service_id);
                                                const discountTotal = a.appointmentServices
                                                    .filter(s => matchedServices.includes(s.appointment_service_service_id))
                                                    .reduce((sum, s) => sum + Number(s.appointment_service_price || 0), 0);

                                                const finalTotal = Number(a.total_paid || 0) - discountTotal;

                                                if (discountTotal > 0) {
                                                    return (
                                                        <div className="text-sm text-gray-700 dark:text-gray-200 mt-2 space-y-1">
                                                            <div>Total: {a.currency_symbol}{Number(a.total_paid).toFixed(2)}</div>
                                                            <div>Discount: {a.currency_symbol}{discountTotal.toFixed(2)}</div>
                                                            <div className="font-semibold">Final: {a.currency_symbol}{finalTotal.toFixed(2)}</div>
                                                        </div>
                                                    );
                                                } else {
                                                    // No discount, show only total_paid
                                                    return (
                                                        <div className="text-sm text-gray-700 dark:text-gray-200 mt-2">
                                                            {a.currency_symbol}{Number(a.total_paid).toFixed(2)}
                                                        </div>
                                                    );
                                                }
                                            })()}
                                            {/* {a.currency_symbol}{Number(a.total_paid).toFixed(2)} */}

                                            </td>

                                        <td>
                                            <div>
                                                    {a.appoinmentUsedPlan && a.appoinmentUsedPlan.length > 0 && (() => {
                                                        // Group services by plan name
                                                        const groupedPlans: { [key: string]: string[] } = {};
                                                        a.appoinmentUsedPlan.forEach((item: any) => {
                                                            if (!groupedPlans[item.name]) {
                                                                groupedPlans[item.name] = [];
                                                            }
                                                            groupedPlans[item.name].push(item.service_name);
                                                        });

                                                        return (
                                                            <div className='ml-2 badge badge-outline-success text-left'>
                                                                {Object.entries(groupedPlans).map(([planName, serviceNames], idx) => (
                                                                    <div key={idx} className="mb-1">
                                                                        <strong>{planName}</strong>
                                                                        <ul className="ml-2">
                                                                            {serviceNames.map((service, i) => (
                                                                                <li key={i}>{service}</li>
                                                                            ))}
                                                                        </ul>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        );
                                                    })()}
                                                </div>

                                            <div className='my-2'>

                                                {a.appointmentServices.length > 0 && (
                                                    <div className='ml-2 badge badge-outline-info'>
                                                        {a.appointmentServices.map((appoinmentService) => (

                                                            <>
                                                                <div><span>{appoinmentService.appointment_service} : {a.currency_symbol}{appoinmentService.appointment_service_price}</span></div>

                                                                {appoinmentService.appointment_service_notes && (
                                                                    <div><span>{appoinmentService.appointment_service_notes}</span></div>
                                                                )}
                                                            </>
                                                        ))}
                                                    </div>
                                                )}
                                            </div>

                                            <div className='my-2'><span className={`px-2 py-1 rounded text-xs font-semibold ${a.status === 'completed' ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' : a.status === 'pending' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'}`}>{formatStatus(a.status)}</span></div>

                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>


            </div>

            {/* Plan History Section */}
            <div className="bg-white dark:bg-gray-900 rounded-xl shadow p-6 mb-8">
                <h5 className="font-semibold text-lg mb-4 text-gray-800 dark:text-white">Purchased Plan History</h5>
                <div className="overflow-x-auto">
                    <table className="min-w-full text-sm">
                        <thead>
                            <tr className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-200">
                                <th className="p-2">Plan</th>
                                <th className="p-2">Price</th>
                                <th className="p-2">Validity</th>
                                <th className="p-2">Status</th>
                                <th className="p-2">Purchased At</th>
                                <th className="p-2">Service</th>
                            </tr>
                        </thead>
                        <tbody>
                            {planHistory.length === 0 && (
                                <tr><td colSpan={6} className="text-center p-4 text-gray-400 dark:text-gray-500">No plan history found.</td></tr>
                            )}
                            {planHistory.map((plan) => (
                                <tr key={plan.id} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800">
                                    <td className="p-2 text-gray-700 dark:text-gray-200">
                                     <div className="space-y-1">{plan.name}</div>

                                    </td>
                                    <td className="p-2 text-gray-700 dark:text-gray-200">{plan.currency_symbol}{Number(plan.price).toFixed(2)}</td>
                                    <td className="p-2 text-gray-700 dark:text-gray-200">{plan.validity_days} days</td>
                                    <td className="p-2"><span className={`px-2 py-1 rounded text-xs font-semibold ${plan.status === 'active' ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' : plan.status === 'pending' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'}`}>{plan.status}</span></td>
                                    <td className="p-2 text-gray-700 dark:text-gray-200">
                                    <div>Purchase at : {new Date(plan.purchased_at).toLocaleDateString()}</div>
                                    <div>Expire at : {new Date(plan.expires_at).toLocaleDateString()}</div>

                                    </td>
                                    <td  className="p-2 text-gray-700 dark:text-gray-200">
                                        <div className="space-y-1">
                                                {plan.planServiceUsages.map((serviceUsage) => (
                                                    <>
                                                        <div key={serviceUsage.id} className="flex items-center justify-between text-sm">
                                                            <span>{serviceUsage.service.name}</span>
                                                            <span className="ml-2 badge badge-outline-success">
                                                                {(serviceUsage.used_count > 0) && (
                                                                    <span className="me-2 text-danger">
                                                                        {serviceUsage.used_count} used
                                                                    </span>
                                                                )}
                                                                {serviceUsage.remaining_count} remaining
                                                            </span>

                                                        </div>
                                                    </>
                                                ))}
                                            </div>
                                    </td>

                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};

export default Dashboard;
