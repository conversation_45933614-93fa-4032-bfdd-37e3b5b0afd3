<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Inertia\Inertia;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        URL::forceScheme('https');

        // Share Ziggy routes with Inertia
        Inertia::share([
            'ziggy' => function () {
                return [
                    'url'    => config('app.url'),
                    'port'   => parse_url(config('app.url'), PHP_URL_PORT),
                    'routes' => app('router')->getRoutes()->getRoutesByName(),
                ];
            },
        ]);
    }
}
