<?php

declare(strict_types=1);

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Services\DashboardService;
use Illuminate\Http\Request;
use Inertia\Inertia;

final class DashboardController extends Controller
{
    public function __construct(private readonly DashboardService $dashboardService) {}

    public function index(Request $request)
    {

        if ($request->wantsJson()) {
            $branchId = (int) $request->branch_id;
        } else {
            $user     = auth()->user();
            $branchId = $user->current_branch_id;
        }

        // If user doesn't have a branch (admin/customer), show basic dashboard
        if (! $branchId) {
            return Inertia::render('Vendor/Dashboard', [
                'appointmentStats'        => [],
                'seatStats'               => [],
                'upcomingAppointments'    => [],
                'recentCompletedServices' => [],
                'revenueStats'            => [],
                'serviceAnalytics'        => [],
                'customerInsights'        => [],
                'userRole'                => $user->roles->pluck('name')->first() ?? 'user',
                'message'                 => 'Welcome to the dashboard!',
                'currency_symbol'         => '₹',
                'currency_text'           => 'INR',
            ]);
        }

        $dashboardData   = $this->dashboardService->getDashboardData($branchId);
        $branch          = \App\Models\Branch::find($branchId);
        $currency_symbol = $branch?->currency_symbol ?? '₹';
        $currency_text   = $branch?->currency_text   ?? 'INR';

        if ($request->wantsJson()) {
            return response()->json([
                'message' => 'Dashboard data retrived',
                ...$dashboardData,
                'currency_symbol' => $currency_symbol,
                'currency_text'   => $currency_text,
            ]);
        } else {
            return Inertia::render('Vendor/Dashboard', [
                ...$dashboardData,
                'currency_symbol' => $currency_symbol,
                'currency_text'   => $currency_text,
            ]);
        }

    }
}
