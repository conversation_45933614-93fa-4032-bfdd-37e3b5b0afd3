<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AppointmentBookedMail extends Mailable
{
    use Queueable, SerializesModels;

    public $appointment;

    public $customer;

    public $branch;

    public $vendor;

    public $isForVendor;

    /**
     * Create a new message instance.
     */
    public function __construct($appointment, $customer, $branch, $vendor = null, $isForVendor = false)
    {
        $this->appointment = $appointment;
        $this->customer    = $customer;
        $this->branch      = $branch;
        $this->vendor      = $vendor;
        $this->isForVendor = $isForVendor;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        $subject = $this->isForVendor
            ? 'New Appointment Request Received'
            : 'Your Appointment is Booked!';

        return $this->subject($subject)
            ->view('emails.appointment_booked');
    }
}
