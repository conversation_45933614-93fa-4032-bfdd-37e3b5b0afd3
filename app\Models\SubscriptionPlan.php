<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

final class SubscriptionPlan extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'price',
        'currency_symbol',
        'currency_code',
        'billing_cycle',
        'max_services',
        'max_appointments_per_month',
        'max_seats',
        'max_branches',
        'max_staff',
        'has_analytics',
        'has_api_access',
        'has_custom_branding',
        'has_priority_support',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'price'                      => 'float',
        'max_services'               => 'integer',
        'max_appointments_per_month' => 'integer',
        'max_seats'                  => 'integer',
        'max_branches'               => 'integer',
        'max_staff'                  => 'integer',
        'has_analytics'              => 'boolean',
        'has_api_access'             => 'boolean',
        'has_custom_branding'        => 'boolean',
        'has_priority_support'       => 'boolean',
        'is_active'                  => 'boolean',
        'sort_order'                 => 'integer',
    ];

    public function vendorSubscriptions(): HasMany
    {
        return $this->hasMany(VendorSubscription::class);
    }

    public function activeVendorSubscriptions(): HasMany
    {
        return $this->hasMany(VendorSubscription::class)->where('status', 'active');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    public function isUnlimited(string $feature): bool
    {
        return $this->{$feature} === 0;
    }

    public function getFormattedPriceAttribute(): string
    {
        return \App\Helpers\CurrencyHelper::formatINR($this->price);
    }

    public function getBillingCycleLabelAttribute(): string
    {
        return ucfirst($this->billing_cycle);
    }

    public function getFeaturesList(): array
    {
        $features = [];

        if ($this->isUnlimited('max_services')) {
            $features[] = 'Unlimited Services';
        } else {
            $features[] = "Up to {$this->max_services} Services";
        }

        if ($this->isUnlimited('max_appointments_per_month')) {
            $features[] = 'Unlimited Appointments';
        } else {
            $features[] = "Up to {$this->max_appointments_per_month} Appointments/month";
        }

        if ($this->isUnlimited('max_seats')) {
            $features[] = 'Unlimited Seats';
        } else {
            $features[] = "Up to {$this->max_seats} Seats";
        }

        if ($this->max_branches > 1) {
            $features[] = "Up to {$this->max_branches} Branches";
        } else {
            $features[] = '1 Branch';
        }

        if ($this->isUnlimited('max_staff')) {
            $features[] = 'Unlimited Staff';
        } else {
            $features[] = "Up to {$this->max_staff} Staff Members";
        }

        if ($this->has_analytics) {
            $features[] = 'Advanced Analytics';
        }

        if ($this->has_api_access) {
            $features[] = 'API Access';
        }

        if ($this->has_custom_branding) {
            $features[] = 'Custom Branding';
        }

        if ($this->has_priority_support) {
            $features[] = 'Priority Support';
        }

        return $features;
    }
}
