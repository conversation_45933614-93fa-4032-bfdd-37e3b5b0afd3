import React, { useEffect } from 'react';
import { Link, useForm } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';

const Create = () => {
    const dispatch = useDispatch();
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        description: '',
        price: '',
        currency_symbol: '₹',
        currency_code: 'INR',
        billing_cycle: 'monthly',
        max_services: 0,
        max_branches: 1,
        max_staff: 0,
        is_active: true,
        sort_order: 0,
    });

    useEffect(() => {
        dispatch(setPageTitle('Create Subscription Plan'));
    }, [dispatch]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('siteadmin.subscription-plans.store'));
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.subscription-plans.index')} className="text-primary hover:underline">
                        Subscription Plans
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Create</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Create Subscription Plan</h5>
                    </div>
                    <form className="space-y-5" onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="name">Name <span className="text-danger">*</span></label>
                                <input id="name" type="text" className="form-input" value={data.name} onChange={e => setData('name', e.target.value)} required />
                                {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                            </div>
                            <div>
                                <label htmlFor="currency_symbol">Currency Symbol <span className="text-danger">*</span></label>
                                <input id="currency_symbol" type="text" className="form-input" value={data.currency_symbol} onChange={e => setData('currency_symbol', e.target.value)} required maxLength={8} />
                                {errors.currency_symbol && <div className="text-danger mt-1">{errors.currency_symbol}</div>}
                            </div>
                            <div>
                                <label htmlFor="currency_code">Currency Code <span className="text-danger">*</span></label>
                                <input id="currency_code" type="text" className="form-input" value={data.currency_code} onChange={e => setData('currency_code', e.target.value)} required maxLength={8} />
                                {errors.currency_code && <div className="text-danger mt-1">{errors.currency_code}</div>}
                            </div>
                            <div>
                                <label htmlFor="price">Price <span className="text-danger">*</span></label>
                                <input id="price" type="number" min="0" step="0.01" className="form-input" value={data.price} onChange={e => setData('price', e.target.value)} required />
                                {errors.price && <div className="text-danger mt-1">{errors.price}</div>}
                            </div>
                            <div>
                                <label htmlFor="billing_cycle">Billing Cycle <span className="text-danger">*</span></label>
                                <select id="billing_cycle" className="form-input" value={data.billing_cycle} onChange={e => setData('billing_cycle', e.target.value)} required>
                                    <option value="monthly">Monthly</option>
                                    <option value="yearly">Yearly</option>
                                </select>
                                {errors.billing_cycle && <div className="text-danger mt-1">{errors.billing_cycle}</div>}
                            </div>
                            <div>
                                <label htmlFor="max_services">Max Services <span className="text-danger">*</span></label>
                                <input id="max_services" type="number" min="0" className="form-input" value={data.max_services} onChange={e => setData('max_services', Number(e.target.value))} required />
                                <div className="text-xs text-gray-500">0 = Unlimited</div>
                                {errors.max_services && <div className="text-danger mt-1">{errors.max_services}</div>}
                            </div>
                            <div>
                                <label htmlFor="max_branches">Max Branches <span className="text-danger">*</span></label>
                                <input id="max_branches" type="number" min="1" className="form-input" value={data.max_branches} onChange={e => setData('max_branches', Number(e.target.value))} required />
                                {errors.max_branches && <div className="text-danger mt-1">{errors.max_branches}</div>}
                            </div>
                            <div>
                                <label htmlFor="max_staff">Max Staff <span className="text-danger">*</span></label>
                                <input id="max_staff" type="number" min="0" className="form-input" value={data.max_staff} onChange={e => setData('max_staff', Number(e.target.value))} required />
                                <div className="text-xs text-gray-500">0 = Unlimited</div>
                                {errors.max_staff && <div className="text-danger mt-1">{errors.max_staff}</div>}
                            </div>
                            <div>
                                <label htmlFor="sort_order">Sort Order</label>
                                <input id="sort_order" type="number" className="form-input" value={data.sort_order} onChange={e => setData('sort_order', Number(e.target.value))} />
                                {errors.sort_order && <div className="text-danger mt-1">{errors.sort_order}</div>}
                            </div>
                            <div className="flex items-center mt-6">
                                <input type="checkbox" className="form-checkbox mr-2" checked={data.is_active} onChange={e => setData('is_active', e.target.checked)} />
                                <label>Active</label>
                                {errors.is_active && <div className="text-danger mt-1">{errors.is_active}</div>}
                            </div>
                        </div>
                        <div className="flex items-center justify-end gap-4 mt-4">
                            <Link href={route('siteadmin.subscription-plans.index')} className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary" disabled={processing}>
                                {processing ? 'Creating...' : 'Create Plan'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Create; 