import { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import { IRootState } from '@/store';
import { setPageTitle } from '@/store/themeConfigSlice';
import PerfectScrollbar from 'react-perfect-scrollbar';

interface Props {
    purchases: {
        data: Array<{
            id: number;
            user: {
                id: number;
                name: string;
                email: string;
                phone: string;
            };
            plan: {
                id: number;
                name: string;
                price: number;
                validity_days: number;
            };
            expires_at: string;
            purchased_at: string;
            currency_symbol: string;
            currency_text: string;
            created_at: string;
            status: string;
            planServiceUsages: Array<{
                id: number;
                service: {
                    id: number;
                    name: string;
                };
                remaining_count: number;
                used_count: number;
            }>;
        }>;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search: string;
        status: string;
        sort: string;
        direction: string;
    };
}

const Purchases = ({ purchases, filters }: Props) => {
    const dispatch = useDispatch();
    const [search, setSearch] = useState(filters.search);
    const [status, setStatus] = useState(filters.status);

    useEffect(() => {
        dispatch(setPageTitle('Purchased Plans'));
    });

    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(
            route('vendor.purchased-plan.purchases'),
            { search: value, status, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleStatusChange = (value: string) => {
        setStatus(value);
        router.get(
            route('vendor.purchased-plan.purchases'),
            { search, status: value, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleUpdateStatus = (planId: number, newStatus: string) => {
        router.post(route('vendor.purchased-plan.update-status', planId), {
            status: newStatus
        }, {
            preserveState: true,
            preserveScroll: true,
            onSuccess: () => {
                // Optionally show a success message
            }
        });
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active':
                return 'success';
            case 'pending':
                return 'warning';
            case 'expired':
                return 'danger';
            case 'cancelled':
                return 'danger';
            default:
                return 'primary';
        }
    };

    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl' ? true : false;

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Purchased Plans</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex md:flex-row flex-col md:items-center gap-5 mb-5">
                        <div className="flex flex-1 items-center gap-5">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="py-2 ltr:pr-11 rtl:pl-11 form-input peer"
                                    value={search}
                                    onChange={(e) => handleSearch(e.target.value)}
                                />
                                <button type="button" className="top-1/2 ltr:right-[11px] rtl:left-[11px] absolute peer-focus:text-primary -translate-y-1/2">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11.5" cy="11.5" r="9.5" stroke="currentColor" strokeWidth="1.5" opacity="0.5"></circle>
                                        <path d="M18.5 18.5L22 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                                    </svg>
                                </button>
                            </div>
                            <div className="flex items-center gap-5">
                                <select
                                    className="w-32 form-select"
                                    value={status}
                                    onChange={(e) => handleStatusChange(e.target.value)}
                                >
                                    <option value="">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="active">Active</option>
                                    <option value="expired">Expired</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Plan</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Services</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {purchases.data.map((purchase) => (
                                    <tr key={purchase.id}>
                                        <td>
                                            <div className="font-semibold">{purchase.user.name}</div>
                                            <div className="text-gray-500 text-xs">{purchase.user.email}</div>
                                            <div className="text-gray-500 text-xs">{purchase.user.phone}</div>
                                        </td>
                                        <td>
                                            <div className="font-semibold">{purchase.plan.name}</div>
                                            <div className="text-gray-500 text-xs">{purchase.currency_symbol}{purchase.plan.price}</div>
                                            <div className="text-gray-500 text-xs">{purchase.plan.validity_days} days</div>
                                        </td>
                                        <td>
                                            <div>Purchase at : {new Date(purchase.purchased_at).toLocaleDateString()}</div>
                                            <div>Expire at : {new Date(purchase.expires_at).toLocaleDateString()}</div>
                                        </td>
                                        <td>
                                            <span className={`badge badge-outline-${getStatusColor(purchase.status)}`}>
                                                {purchase.status.charAt(0).toUpperCase() + purchase.status.slice(1)}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="space-y-1">
                                                {purchase.planServiceUsages.map((serviceUsage) => (
                                                    <>
                                                        <div key={serviceUsage.id} className="flex justify-between items-center text-sm">
                                                            <span>{serviceUsage.service.name}</span>
                                                            <span className="ml-2 badge badge-outline-success">
                                                                {(serviceUsage.used_count > 0) && (
                                                                    <span className="text-danger me-2">
                                                                        {serviceUsage.used_count} used
                                                                    </span>
                                                                )}
                                                                {serviceUsage.remaining_count} remaining
                                                            </span>

                                                        </div>
                                                    </>
                                                ))}
                                            </div>
                                        </td>
                                        <td>
                                            <select
                                                className="form-select"
                                                value={purchase.status}
                                                onChange={(e) => handleUpdateStatus(purchase.id, e.target.value)}
                                            >
                                                <option value="pending">Pending</option>
                                                <option value="active">Active</option>
                                                <option value="expired">Expired</option>
                                                <option value="cancelled">Cancelled</option>
                                            </select>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    <div className="mt-6">
                        <div className="flex flex-wrap justify-center items-center gap-4">
                            {purchases.links.map((link, i) => (
                                <Link
                                    key={i}
                                    href={link.url || '#'}
                                    className={`px-4 py-2 text-sm font-semibold rounded-md ${link.active
                                            ? 'bg-primary text-white'
                                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    {link.label === '&laquo; Previous' ? 'Previous' :
                                        link.label === 'Next &raquo;' ? 'Next' :
                                            link.label}
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Purchases; 