<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointment_services', function (Blueprint $table) {
            $table->id();
            $table->foreignId('appointment_id')->constrained('appointments')->onDelete('cascade');
            $table->foreignId('service_id')->constrained('services')->onDelete('cascade');
            $table->foreignId('seat_id')->nullable()->constrained('seats')->onDelete('set null');
            $table->timestamp('start_time')->nullable();
            $table->timestamp('end_time')->nullable();
            $table->timestamp('estimated_end_time')->nullable();
            $table->foreignId('plan_used_service')->nullable()->constrained('plan_service_usages')->onDelete('set null');
            $table->text('service_notes')->nullable();
            $table->decimal('price', 8, 2)->nullable();
            $table->string('service_name')->nullable();
            $table->integer('notification_after_service_count')->default(0);
            $table->integer('notification_status')->default(0)->comment('0:default,1:sent');
            $table->timestamp('notification_date_after_service')->nullable();
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('appointment_services');
        Schema::enableForeignKeyConstraints();
    }
};
