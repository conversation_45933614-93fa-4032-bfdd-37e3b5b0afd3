<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Appointment;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;

class UserDataService
{
    public function getVendorAttachedCustomerIds($branchId)
    {

        $appoinmentUsers = Appointment::where('branch_id', $branchId)
            ->distinct()
            ->pluck('user_id')
            ->toArray();

        $branchRelatedUserIDs = User::whereRaw('FIND_IN_SET(?, customer_branch_ids)', [$branchId])
            ->distinct()
            ->pluck('id')
            ->toArray();

        $allIds = array_unique(array_merge($appoinmentUsers, $branchRelatedUserIDs));

        return $allIds;
    }

    public function getCustomersWithFilters($branchId, $filters = []): LengthAwarePaginator
    {
        $customerIds = $this->getVendorAttachedCustomerIds($branchId);

        $query = User::whereIn('id', $customerIds)
            ->with(['appointments' => function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            }])
            ->withCount(['appointments' => function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            }]);

        // Apply search filter
        if (! empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function (Builder $q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%")
                    ->orWhere('company_name', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if (! empty($filters['status'])) {
            if ($filters['status'] === 'active') {
                $query->where('is_active', true);
            } elseif ($filters['status'] === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Apply gender filter
        if (! empty($filters['gender'])) {
            $query->where('gender', $filters['gender']);
        }

        // Apply sorting
        $sortField     = $filters['sort']      ?? 'id';
        $sortDirection = $filters['direction'] ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        return $query->paginate(env('LIMIT_PAGE', 15));
    }

    public function getCustomerDetails($customerId, $branchId): ?User
    {
        return User::where('id', $customerId)
            ->with(['appointments' => function ($query) use ($branchId) {
                $query->where('branch_id', $branchId)
                    ->with(['services', 'branch'])
                    ->orderBy('appointment_date', 'desc')
                    ->orderBy('appointment_time', 'desc');
            }])
            ->withCount(['appointments' => function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            }])
            ->first();
    }

    public function getCustomerBookingHistory($customerId, $branchId, $filters = []): LengthAwarePaginator
    {
        $query = Appointment::where('user_id', $customerId)
            ->where('branch_id', $branchId)
            ->with(['services', 'branch'])
            ->orderBy('appointment_date', 'desc')
            ->orderBy('appointment_time', 'desc');

        // Apply status filter
        if (! empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        // Apply date range filter
        if (! empty($filters['date_from'])) {
            $query->where('appointment_date', '>=', $filters['date_from']);
        }

        if (! empty($filters['date_to'])) {
            $query->where('appointment_date', '<=', $filters['date_to']);
        }

        // Apply search filter
        if (! empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function (Builder $q) use ($search) {
                $q->where('ticket_number', 'like', "%{$search}%")
                    ->orWhere('notes', 'like', "%{$search}%");
            });
        }

        return $query->paginate(env('LIMIT_PAGE', 15));
    }

    public function getCustomerStats($customerId, $branchId): array
    {
        $appointments = Appointment::where('user_id', $customerId)
            ->where('branch_id', $branchId);

        return [
            'total_appointments'       => $appointments->count(),
            'completed_appointments'   => $appointments->where('status', 'completed')->count(),
            'pending_appointments'     => $appointments->where('status', 'pending')->count(),
            'cancelled_appointments'   => $appointments->where('status', 'cancelled')->count(),
            'in_progress_appointments' => $appointments->where('status', 'in_progress')->count(),
            'first_booking'            => $appointments->orderBy('appointment_date')->first()?->appointment_date,
            'last_booking'             => $appointments->orderBy('appointment_date', 'desc')->first()?->appointment_date,
        ];
    }

    /**
     * Create or update a customer for a branch. Returns [User, message]
     */
    public function createOrUpdateCustomer(array $data, int $branchId): array
    {
        $user    = User::where('email', $data['email'])->first();
        $message = '';
        if ($user) {
            // Existing user - Update customer_branch_ids
            $existingBranchIds = $user->customer_branch_ids ? explode(',', $user->customer_branch_ids) : [];
            if (! in_array($branchId, $existingBranchIds)) {
                $existingBranchIds[]       = $branchId;
                $user->customer_branch_ids = implode(',', array_unique($existingBranchIds));
                $user->save();
                $user->assignRole('customer');
            }
            $message = 'Customer updated successfully.';
        } else {
            // New user - Create record
            $user = User::create([
                'name'                => $data['name'],
                'email'               => $data['email'],
                'phone'               => $data['phone'],
                'gender'              => $data['gender'],
                'address'             => $data['address'] ?? null,
                'is_active'           => true,
                'customer_branch_ids' => $branchId,
                'password'            => bcrypt('password'), // Default password, should be changed later
            ]);
            $user->assignRole('customer');
            $message = 'Customer added successfully.';
        }

        return [$user, $message];
    }
}
