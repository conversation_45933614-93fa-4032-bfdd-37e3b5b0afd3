import { router } from '@inertiajs/react';

/**
 * Utility functions to replace React Router navigation with Inertia.js
 */

// Replace useNavigate hook functionality
export const useInertiaNavigate = () => {
  return (url: string, options?: { replace?: boolean }) => {
    if (options?.replace) {
      router.visit(url, { replace: true });
    } else {
      router.visit(url);
    }
  };
};

// Helper function for programmatic navigation
export const navigateTo = (url: string, options?: { replace?: boolean }) => {
  if (options?.replace) {
    router.visit(url, { replace: true });
  } else {
    router.visit(url);
  }
};
