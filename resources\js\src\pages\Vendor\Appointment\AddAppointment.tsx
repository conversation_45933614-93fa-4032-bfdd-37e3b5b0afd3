import React, { useEffect, useState } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import Swal from 'sweetalert2';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/flatpickr.css';
import { setPageTitle } from '@store/themeConfigSlice';
import AddCustomerModal from '@components/AddCustomerModal';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
}

interface Customer {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface Seat {
    id: number;
    name: string;
}

interface Staff {
    id: number;
    name: string;
    email: string;
    phone: string;
}

interface PageProps {
    services: Service[];
    customers: Customer[];
    allSeats: Seat[];
    staff?: Staff[];
    [key: string]: any;
}

interface FormValues {
    user_id: string;
    appointment_date: string;
    appointment_time: string;
    services: Service[];
    notes: string;
    staff_id?: string;
}

const AddAppointment = () => {
    const dispatch = useDispatch();
    const { services, customers, allSeats, staff = [] } = usePage<PageProps & { staff?: Staff[] }>().props;
    const [showAddCustomerModal, setShowAddCustomerModal] = useState(false);

    // Working hours state
    const [workingHours, setWorkingHours] = useState<any[]>([]);
    const [selectedDay, setSelectedDay] = useState<string>('');
    const [dayClosed, setDayClosed] = useState<boolean>(false);
    const [workingHourError, setWorkingHourError] = useState<string>('');
    const [minTime, setMinTime] = useState<string>('00:00');
    const [maxTime, setMaxTime] = useState<string>('23:59');

    const { props } = usePage();
    const auth = props.auth || {};
    const branch = auth.user?.branch || {};
    const currency_symbol = branch.currency_symbol || '₹';
    const currency_text = branch.currency_text || 'INR';

    useEffect(() => {
        dispatch(setPageTitle('Add Appointment'));
        // Fetch working hours for the current branch
        fetch('/get-working-hours')
            .then(res => res.json())
            .then(data => {
                setWorkingHours(data.workingHours || []);
            });
    }, [dispatch]);

    // Helper to get day name from date string (YYYY-MM-DD)
    const getDayName = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { weekday: 'long' });
    };

    // When user changes date, update working hour info
    const handleDateChange = (date: Date[], setFieldValue: any) => {
        if (date[0]) {
            const year = date[0].getFullYear();
            const month = String(date[0].getMonth() + 1).padStart(2, '0');
            const day = String(date[0].getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;
            setFieldValue('appointment_date', formattedDate);
            // Update available times and closed status
            const dayName = getDayName(formattedDate);
            setSelectedDay(dayName);
            const dayWorkingHour = workingHours.find((wh) => wh.day === dayName);
            if (dayWorkingHour) {
                if (dayWorkingHour.is_closed) {
                    setDayClosed(true);
                    setWorkingHourError(`The branch is closed on ${dayName}. You cannot create appointments for this day.`);
                    setMinTime('00:00');
                    setMaxTime('23:59');
                } else {
                    setDayClosed(false);
                    setWorkingHourError('');
                    setMinTime(dayWorkingHour.open);
                    setMaxTime(dayWorkingHour.close);
                }
            } else {
                setDayClosed(true);
                setWorkingHourError(`No working hours found for ${dayName}.`);
                setMinTime('00:00');
                setMaxTime('23:59');
            }
        }
    };

    const validationSchema = Yup.object().shape({
        user_id: Yup.number().required('Please select a customer'),
        appointment_date: Yup.string()
            .required('Please select an appointment date')
            .test('is-valid-date', 'Appointment date must be today or later', function(value) {
                if (!value) return false;
                const [year, month, day] = value.split('-').map(Number);
                const selectedDate = new Date(year, month - 1, day);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                return selectedDate >= today;
            }),
        appointment_time: Yup.string().required('Please select an appointment time'),
        services: Yup.array()
            .of(
                Yup.object().shape({
                    id: Yup.number().required(),
                    name: Yup.string().required(),
                    price: Yup.number().required(),
                })
            )
            .min(1, 'Please select at least one service')
            .required('Please select at least one service'),
        notes: Yup.string().max(500, 'Notes cannot exceed 500 characters'),
    });

    const initialValues: FormValues & { staff_id?: string } = {
        user_id: '',
        appointment_date: '',
        appointment_time: '',
        services: [],
        notes: '',
        staff_id: '',
    };

    const handleSubmit = (values: any) => {
        router.post('/vendor/appointments', values, {
            onSuccess: () => {
                Swal.fire({
                    icon: 'success',
                    title: 'Appointment created successfully',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                });
            },
            onError: (errors) => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error creating appointment',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                });
            },
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/appointments" className="text-primary hover:underline">
                        Appointments
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Add Appointment</span>
                </li>
            </ul>

            <div className="panel mt-6">
                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                >
                    {({ errors, touched, values, setFieldValue }) => (
                        <Form className="space-y-5">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                {/* Customer Selection */}
                                <div>
                                    <label htmlFor="user_id">Customer</label>
                                    <div className="flex lg:items-center flex-col lg:flex-row gap-4 flex-wrap">
                                        <select
                                            id="user_id"
                                            name="user_id"
                                            className="form-select flex-1"
                                            value={values.user_id}
                                            onChange={(e) => setFieldValue('user_id', e.target.value)}
                                        >
                                            <option value="">Select Customer</option>
                                            {customers.map((customer) => (
                                                <option key={customer.id} value={customer.id}>
                                                    {customer.name} ({customer.email})
                                                </option>
                                            ))}
                                        </select>
                                        <button
                                            type="button"
                                            className="btn btn-outline-primary"
                                            onClick={() => setShowAddCustomerModal(true)}
                                        >
                                            + Add Customer
                                        </button>
                                    </div>
                                    {errors.user_id && touched.user_id && (
                                        <div className="text-danger mt-1">{errors.user_id}</div>
                                    )}
                                </div>

                                {/* Appointment Date */}
                                <div>
                                    <label htmlFor="appointment_date">Appointment Date</label>
                                    <Flatpickr
                                        id="appointment_date"
                                        name="appointment_date"
                                        className="form-input"
                                        value={values.appointment_date}
                                        onChange={(date) => handleDateChange(date, setFieldValue)}
                                        options={{
                                            dateFormat: 'Y-m-d',
                                            minDate: 'today',
                                            disableMobile: true,
                                            time_24hr: true,
                                            enableTime: false,
                                        }}
                                    />
                                    {errors.appointment_date && touched.appointment_date && (
                                        <div className="text-danger mt-1">{errors.appointment_date}</div>
                                    )}
                                </div>

                                {/* Appointment Time */}
                                <div>
                                    <label htmlFor="appointment_time">Appointment Time</label>
                                    <Flatpickr
                                        id="appointment_time"
                                        name="appointment_time"
                                        className="form-input"
                                        value={values.appointment_time}
                                        onChange={(time) => {
                                            if (time[0]) {
                                                setFieldValue('appointment_time', time[0].toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true }));
                                            }
                                        }}
                                        options={{
                                            enableTime: true,
                                            noCalendar: true,
                                            dateFormat: 'h:i K', // 12-hour format with AM/PM
                                            time_24hr: false,
                                            disableMobile: true,
                                            minTime: minTime,
                                            maxTime: maxTime,
                                            minuteIncrement: 15,
                                        }}
                                        disabled={dayClosed}
                                    />
                                    {errors.appointment_time && touched.appointment_time && (
                                        <div className="text-danger mt-1">{errors.appointment_time}</div>
                                    )}
                                </div>

                                {/* Show error if day is closed */}
                                {workingHourError && (
                                    <div className="text-danger mb-2">{workingHourError}</div>
                                )}

                                {/* Staff Selection */}
                                {staff.length > 0 && (
                                    <div>
                                        <label htmlFor="staff_id">Select Staff</label>
                                        <select
                                            id="staff_id"
                                            name="staff_id"
                                            className="form-select"
                                            value={values.staff_id}
                                            onChange={(e) => setFieldValue('staff_id', e.target.value)}
                                        >
                                            <option value="">Select Staff</option>
                                            {staff.map((s) => (
                                                <option key={s.id} value={s.id}>
                                                    {s.name} ({s.email})
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                )}

                                {/* Services Selection */}
                                <div className="md:col-span-2">
                                    <label>Services</label>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                                        {services.map((service) => (
                                            <div key={service.id} className="flex items-center">
                                                <input
                                                    type="checkbox"
                                                    id={`service-${service.id}`}
                                                    className="form-checkbox"
                                                    checked={values.services.some((s) => s.id === service.id)}
                                                    onChange={(e) => {
                                                        const newServices = e.target.checked
                                                            ? [...values.services, service]
                                                            : values.services.filter((s) => s.id !== service.id);
                                                        setFieldValue('services', newServices);
                                                    }}
                                                />
                                                <label htmlFor={`service-${service.id}`} className="ml-2">
                                                    {service.name} - {currency_symbol}{service.price}
                                                </label>
                                            </div>
                                        ))}
                                    </div>
                                    {errors.services && touched.services && (
                                        <div className="text-danger mt-1">
                                            {typeof errors.services === 'string' ? errors.services : 'Please select at least one service'}
                                        </div>
                                    )}
                                </div>

                                {/* Notes */}
                                <div className="md:col-span-2">
                                    <label htmlFor="notes">Notes</label>
                                    <textarea
                                        id="notes"
                                        name="notes"
                                        className="form-textarea"
                                        rows={4}
                                        value={values.notes}
                                        onChange={(e) => setFieldValue('notes', e.target.value)}
                                    ></textarea>
                                    {errors.notes && touched.notes && (
                                        <div className="text-danger mt-1">{errors.notes}</div>
                                    )}
                                </div>
                            </div>

                            <div className="flex justify-end gap-4 mt-6">
                                <Link href="/vendor/appointments" className="btn btn-outline-danger">
                                    Cancel
                                </Link>
                                <button type="submit" className="btn btn-primary" disabled={dayClosed}>
                                    Create Appointment
                                </button>
                            </div>
                        </Form>
                    )}
                </Formik>
            </div>

            <AddCustomerModal
                open={showAddCustomerModal}
                onClose={() => setShowAddCustomerModal(false)}
                onSuccess={() => {
                    setShowAddCustomerModal(false);
                    router.reload({ only: ['customers'] });
                }}
            />
        </div>
    );
};

export default AddAppointment;
