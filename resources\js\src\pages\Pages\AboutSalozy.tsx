import React from 'react';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';
import { setPageTitle } from '@/store/themeConfigSlice';
import { FaUsers, FaCalendarAlt, FaUserTie, FaChartBar, FaGem, FaImages, FaCloud, FaCheck } from 'react-icons/fa';

const featureList = [
  { icon: <FaUsers className="mb-2 text-2xl text-orange-800" />, label: 'Multi-branch management' },
  { icon: <FaCalendarAlt className="mb-2 text-2xl text-orange-800" />, label: 'Appointment scheduling' },
  { icon: <FaUserTie className="mb-2 text-2xl text-orange-800" />, label: 'Staff & service management' },
  { icon: <FaChartBar className="mb-2 text-2xl text-orange-800" />, label: 'Customer insights & analytics' },
  { icon: <FaGem className="mb-2 text-2xl text-orange-800" />, label: 'Flexible subscription plans' },
  { icon: <FaImages className="mb-2 text-2xl text-orange-800" />, label: 'Gallery & slider for your work' },
  { icon: <FaCloud className="mb-2 text-2xl text-orange-800" />, label: 'Secure, scalable cloud platform' },
];

interface SubscriptionPlan {
  id: number;
  name: string;
  description: string;
  price: number;
  currency_symbol: string;
  currency_code: string;
  billing_cycle: string;
  max_services: number;
  max_appointments_per_month: number;
  max_seats: number;
  max_branches: number;
  max_staff: number;
  has_analytics: boolean;
  has_api_access: boolean;
  has_custom_branding: boolean;
  has_priority_support: boolean;
  sort_order: number;
}

interface PaymentMethod {
  id: number;
  name: string;
  type: string;
  mode: string;
}

interface AuthUser {
  id: number;
  name: string;
  email: string;
  roles: string[];
}

interface ActiveSubscription {
  id: number;
  plan_name: string;
  ends_at: string;
  days_remaining: number;
}

interface Props {
  subscriptionPlans?: SubscriptionPlan[];
  paymentMethods?: PaymentMethod[];
  authUser?: AuthUser | null;
  hasActiveSubscription?: boolean;
  activeSubscription?: ActiveSubscription | null;
  crmInfo?: {
    title: string;
    description: string;
    features: string[];
    cta: string;
  };
}

const AboutSalozy: React.FC<Props> = ({ subscriptionPlans = [], paymentMethods = [], authUser, hasActiveSubscription = false, activeSubscription, crmInfo }) => {
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setPageTitle('Welcome to Salozy CRM'));
  }, [dispatch]);

  const handleGetStarted = (planId: number) => {
    // If user is not logged in, redirect to login
    if (!authUser) {
      window.location.href = '/auth/login';
      return;
    }

    // If user is logged in but doesn't have vendor role, redirect to login
    if (!authUser.roles.includes('vendor')) {
      window.location.href = '/auth/login';
      return;
    }

    // Check if user already has an active subscription
    if (hasActiveSubscription) {
      // Show alert instead of proceeding
      alert(`You already have an active subscription to ${activeSubscription?.plan_name} plan that expires on ${activeSubscription?.ends_at} (${activeSubscription?.days_remaining} days remaining).\n\nIf you want to deactivate your current plan and purchase a new one, please contact the administrator for assistance.`);
      return;
    }

    // If user is logged in and has vendor role, proceed to checkout
    window.location.href = `/checkout/subscription/${planId}`;
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="relative flex flex-col justify-center items-center px-6 py-16 min-h-screen text-center overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Floating Circles */}
          <div className="absolute top-20 left-10 w-20 h-20 bg-primary/5 rounded-full animate-float-slow"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-blue-500/5 rounded-full animate-float-medium"></div>
          <div className="absolute bottom-40 left-20 w-24 h-24 bg-purple-500/5 rounded-full animate-float-slow"></div>
          <div className="absolute bottom-20 right-10 w-12 h-12 bg-green-500/5 rounded-full animate-float-fast"></div>
          <div className="absolute top-60 left-1/3 w-8 h-8 bg-pink-500/5 rounded-full animate-float-medium"></div>
          <div className="absolute top-80 right-1/3 w-14 h-14 bg-indigo-500/5 rounded-full animate-float-slow"></div>

          {/* Geometric Shapes */}
          <div className="absolute top-32 right-40 w-6 h-6 bg-primary/10 rotate-45 animate-rotate-slow"></div>
          <div className="absolute bottom-60 left-40 w-8 h-8 bg-blue-500/10 rotate-45 animate-rotate-reverse"></div>
          <div className="absolute top-96 left-1/4 w-4 h-4 bg-purple-500/10 rotate-45 animate-rotate-slow"></div>

          {/* Gradient Overlay for depth */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-white/30"></div>
        </div>

        <div className="relative z-10 mb-8">
          <img src="/assets/images/logo-icon.svg" alt="Salozy Logo" className="mx-auto w-24 h-24" />
        </div>

        <div className="relative z-10">
          <h1 className="mb-6 font-bold text-4xl text-gray-900 sm:text-5xl lg:text-6xl">
            Salozy CRM
          </h1>
          <p className="mb-4 font-medium text-xl text-orange-800 sm:text-2xl">Professional Salon & Spa Management</p>
          <p className="mx-auto mb-12 max-w-2xl text-gray-700 text-lg leading-relaxed">
            Streamline your business operations with our comprehensive management platform. Handle appointments, staff, services, and customer relationships with ease.
          </p>
        </div>

        {/* Show Sign In and Sign Up buttons only for guest users */}
        {!authUser && (
          <div className="relative z-10 flex sm:flex-row flex-col justify-center gap-4 mb-16">
            <a
              href="/auth/login"
              className="inline-block bg-white border-2 border-orange-800 hover:bg-orange-800 px-8 py-3 rounded-lg font-semibold text-orange-800 hover:text-white transition-all duration-200"
            >
              Sign In
            </a>
            <a
              href="/auth/register"
              className="inline-block bg-orange-800 hover:bg-orange-900 px-8 py-3 rounded-lg font-semibold text-white transition-all duration-200"
            >
              Get Started Free
            </a>
          </div>
        )}

        {/* Show welcome message for logged in users */}
        {authUser && (
          <div className="relative z-10 mb-16">
            <div className="bg-gray-50/90 backdrop-blur-sm px-8 py-6 border border-gray-200 rounded-lg max-w-2xl mx-auto">
              <p className="mb-3 text-gray-800 text-lg">
                Welcome back, <span className="font-semibold text-orange-800">{authUser.name}</span>
              </p>

              {/* Show subscription status */}
              {hasActiveSubscription && activeSubscription ? (
                <div className="mt-4">
                  <p className="font-medium text-green-800 mb-2">
                    Active Plan: <span className="font-semibold">{activeSubscription.plan_name}</span>
                  </p>
                  <p className="text-gray-700 text-sm mb-2">
                    Valid until {activeSubscription.ends_at} ({activeSubscription.days_remaining} days remaining)
                  </p>
                  <p className="text-gray-600 text-sm">
                    Contact administrator to modify your subscription plan.
                  </p>
                </div>
              ) : (
                <p className="text-gray-700">
                  {authUser.roles.includes('vendor')
                    ? 'Select a subscription plan below to activate your business account.'
                    : 'Contact support to upgrade your account for vendor access.'
                  }
                </p>
              )}
            </div>
          </div>
        )}



      </section>

      {/* Subscription Plans Section */}
      {subscriptionPlans.length > 0 && (
        <section className="bg-gray-50 px-6 py-20">
          <div className="mx-auto max-w-6xl">
            {/* Active Subscription Alert */}
            {hasActiveSubscription && activeSubscription && (
              <div className="bg-orange-50 mb-12 p-6 border border-orange-200 rounded-lg">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <svg className="w-5 h-5 text-orange-600" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="font-semibold text-orange-800 mb-2">Active Subscription</h3>
                    <p className="text-orange-700 text-sm mb-2">
                      You have an active <span className="font-medium">{activeSubscription.plan_name}</span> subscription until {activeSubscription.ends_at} ({activeSubscription.days_remaining} days remaining).
                    </p>
                    <p className="text-orange-600 text-sm">
                      Contact <a href="mailto:<EMAIL>" className="font-medium underline"><EMAIL></a> to modify your subscription.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="mb-16 text-center">
              <h2 className="mb-4 font-bold text-3xl text-gray-900 sm:text-4xl">
                Subscription Plans
              </h2>
              <p className="mx-auto max-w-2xl text-gray-700">
                Choose the plan that fits your business needs. All plans include essential features with scalable limits.
              </p>
            </div>

            <div className="gap-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {subscriptionPlans.map((plan) => (
                <div key={plan.id} className="relative bg-white p-8 border border-gray-200 rounded-lg hover:border-orange-800 transition-colors duration-200">
                  {plan.sort_order === 2 && (
                    <div className="-top-3 left-1/2 absolute transform -translate-x-1/2">
                      <span className="bg-orange-800 px-3 py-1 rounded-full font-medium text-sm text-white">
                        Popular
                      </span>
                    </div>
                  )}

                  <div className="mb-8">
                    <h3 className="mb-3 font-semibold text-xl text-gray-900">{plan.name}</h3>
                    <p className="mb-6 text-gray-700 text-sm leading-relaxed">{plan.description}</p>

                    <div className="mb-6">
                      <span className="font-bold text-3xl text-gray-900">{plan.currency_symbol}{plan.price.toLocaleString()}</span>
                      <span className="text-gray-600 ml-1">/{plan.billing_cycle}</span>
                    </div>
                  </div>

                  <ul className="space-y-3 mb-8">
                    <li className="flex items-center text-sm text-gray-800">
                      <FaCheck className="mr-3 text-green-700 flex-shrink-0" />
                      <span>{plan.max_services === 0 ? 'Unlimited' : plan.max_services} Services</span>
                    </li>
                    <li className="flex items-center text-sm text-gray-800">
                      <FaCheck className="mr-3 text-green-700 flex-shrink-0" />
                      <span>{plan.max_branches} {plan.max_branches === 1 ? 'Branch' : 'Branches'}</span>
                    </li>
                    <li className="flex items-center text-sm text-gray-800">
                      <FaCheck className="mr-3 text-green-700 flex-shrink-0" />
                      <span>{plan.max_staff === 0 ? 'Unlimited' : plan.max_staff} Staff Members</span>
                    </li>
                  </ul>

                  <button
                    onClick={() => handleGetStarted(plan.id)}
                    disabled={hasActiveSubscription}
                    className={`block w-full text-center py-3 px-6 rounded-lg font-medium text-sm transition-all duration-200 ${hasActiveSubscription
                      ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
                      : plan.sort_order === 2
                        ? 'bg-orange-800 hover:bg-orange-900 text-white'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300'
                      }`}
                  >
                    Get Started
                  </button>

                  {/* Info note for active subscription */}
                  {/*{hasActiveSubscription && (*/}
                  {/*  <div className="bg-yellow-50 mt-3 p-2 border border-yellow-200 rounded-lg">*/}
                  {/*    <p className="text-xs text-yellow-700">*/}
                  {/*      You already have an active subscription. To deactivate your current plan and purchase a new one, please contact the administrator.*/}
                  {/*    </p>*/}
                  {/*  </div>*/}
                  {/*)}*/}
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Footer Section */}
      <footer className='bg-white px-6 py-8 border-t border-gray-200'>
        <div className="max-w-6xl mx-auto text-center">
          <p className="text-gray-600 text-sm">© {new Date().getFullYear()} Salozy. All rights reserved.</p>
        </div>
      </footer>

    </div>
  );
};

// @ts-expect-error
AboutSalozy.layout = 'blank';

export default AboutSalozy;
