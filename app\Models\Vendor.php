<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

final class Vendor extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'company_name',
        'party_code',
        'contact_person_name',
        'contact_person_number',
        'email',
        'gstin',
        'country_id',
        'state_id',
        'city_id',
        'pincode',
        'address',
        'description',
        'pan_number',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('company_name', 'like', "%{$search}%")
                ->orWhere('party_code', 'like', "%{$search}%")
                ->orWhere('contact_person_name', 'like', "%{$search}%")
                ->orWhere('gstin', 'like', "%{$search}%")
                ->orWhere('pan_number', 'like', "%{$search}%");
        });
    }

    public function scopeSort($query, $field, $direction)
    {
        return $query->orderBy($field, $direction);
    }
}
