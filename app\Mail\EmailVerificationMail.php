<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EmailVerificationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $verificationUrl;

    /**
     * Create a new message instance.
     */
    public function __construct($user)
    {
        $this->user = $user;
        $this->verificationUrl = $this->generateVerificationUrl($user);
    }

    /**
     * Generate the email verification URL
     */
    private function generateVerificationUrl($user)
    {
        $token = base64_encode($user->email . '|' . $user->created_at->timestamp . '|' . config('app.key'));

        // Use URL generation that works in queued context
        try {
            return route('verification.verify', ['token' => $token, 'email' => $user->email]);
        } catch (\Exception $e) {
            // Fallback to manual URL construction if route helper fails
            $baseUrl = config('app.url');
            return $baseUrl . '/auth/verify-email/' . urlencode($token) . '?email=' . urlencode($user->email);
        }
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Verify Your Email Address')
            ->view('emails.email_verification');
    }
}
