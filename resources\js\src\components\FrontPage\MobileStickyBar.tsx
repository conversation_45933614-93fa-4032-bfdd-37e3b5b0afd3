import React from 'react';
import { FaPhone, FaEnvelope, FaShoppingCart, FaCalendarAlt } from 'react-icons/fa';

interface Service {
    id: number;
    name: string;
    description: string;
    duration_minutes: number;
    price: number;
    image: string;
    gender?: 'male' | 'female';
}

interface Tenant {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    logo: string;
}

interface MobileStickyBarProps {
    tenant: Tenant;
    selectedServices: Service[];
    setShowCart: (show: boolean) => void;
    setIsBookingModalOpen: (open: boolean) => void;
}

const MobileStickyBar: React.FC<MobileStickyBarProps> = ({
    tenant,
    selectedServices,
    setShowCart,
    setIsBookingModalOpen
}) => {
    return (
        <div className="right-0 bottom-0 left-0 z-40 fixed md:hidden bg-white/95 shadow-[0_-4px_20px_rgba(0,0,0,0.1)] backdrop-blur-xl border-gray-200 border-t">
            <div className="flex justify-between items-center px-4 py-3">
                <a href={`tel:${tenant.phone}`} className="flex flex-col items-center">
                    <div className="bg-green-600 p-2 rounded-full">
                        <FaPhone className="w-4 h-4 text-white" />
                    </div>
                    <span className="mt-1 font-medium text-xs text-gray-800">Call</span>
                </a>

                <a href={`mailto:${tenant.email}`} className="flex flex-col items-center">
                    <div className="bg-blue-600 p-2 rounded-full">
                        <FaEnvelope className="w-4 h-4 text-white" />
                    </div>
                    <span className="mt-1 font-medium text-xs text-gray-800">Email</span>
                </a>

                <button
                    onClick={() => setShowCart(true)}
                    className="relative flex flex-col items-center"
                >
                    <div className="bg-orange-800 p-2 rounded-full transition-transform duration-200 mobile-cart-icon">
                        <FaShoppingCart className="w-4 h-4 text-white" />
                    </div>
                    {selectedServices.length > 0 && (
                        <span className="-top-1 -right-1 absolute flex justify-center items-center bg-red-500 rounded-full w-5 h-5 text-white text-xs">
                            {selectedServices.length}
                        </span>
                    )}
                    <span className="mt-1 font-medium text-xs text-gray-800">Cart</span>
                </button>

                <button
                    onClick={() => setIsBookingModalOpen(true)}
                    className="flex flex-col items-center"
                >
                    <div className="bg-gray-800 p-2 rounded-full">
                        <FaCalendarAlt className="w-4 h-4 text-white" />
                    </div>
                    <span className="mt-1 font-medium text-xs text-gray-800">Book</span>
                </button>
            </div>
        </div>
    );
};

export default MobileStickyBar;
