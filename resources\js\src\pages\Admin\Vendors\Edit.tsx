import React, { useEffect } from 'react';
import { Link, useForm } from '@inertiajs/react';
import Swal from 'sweetalert2';

interface Vendor {
    id: number;
    name: string;
    email: string;
}

interface Props {
    vendor: Vendor;
    flash?: { success?: string; error?: string };
}

const Edit = ({ vendor, flash }: Props) => {
    const { data, setData, put, processing, errors } = useForm({
        name: vendor.name,
        email: vendor.email,
    });

    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Convert details array to object
        const detailsObj: Record<string, string> = {};
        for (const pair of details) {
            if (pair.key) detailsObj[pair.key] = pair.value;
        }
        // Prevent submit if no details
        if (Object.keys(detailsObj).length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Please add at least one key-value pair in details.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
            return;
        }
        setData('details', detailsObj);
        put(route('siteadmin.vendors.update', { vendor: vendor.id }));
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.vendors.index')} className="text-primary hover:underline">
                        Manage Vendors
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Edit Vendor</h5>
                    </div>
                    <form className="space-y-5" onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="name">Name <span className="text-danger">*</span></label>
                                <input id="name" type="text" className="form-input" value={data.name} onChange={e => setData('name', e.target.value)} required />
                                {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                            </div>
                            <div>
                                <label htmlFor="email">Email <span className="text-danger">*</span></label>
                                <input id="email" type="email" className="form-input" value={data.email} onChange={e => setData('email', e.target.value)} required />
                                {errors.email && <div className="text-danger mt-1">{errors.email}</div>}
                            </div>
                        </div>
                        <div className="flex items-center justify-end gap-4 mt-4">
                            <Link href={route('siteadmin.vendors.index')} className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary" disabled={processing}>
                                {processing ? 'Saving...' : 'Save Changes'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Edit;