import React, { useEffect } from 'react';
import { Link, router } from '@inertiajs/react';
import Swal from 'sweetalert2';
import { useRoute } from '@hooks/useRoute';

interface PaymentMethod {
    id: number;
    name: string;
    type: string;
    mode: 'live' | 'test';
    status: 'active' | 'deactive';
    deleted_at: string;
}

interface Props {
    methods: PaymentMethod[];
    flash?: { success?: string; error?: string };
}

const Trashed = ({ methods, flash }: Props) => {
    const { route } = useRoute();

    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleRestore = (id: number, name: string) => {
        Swal.fire({
            title: 'Are you sure?',
            html: `Are you sure you want to restore the payment method "${name}"?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, restore it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.put(route('siteadmin.payment-methods.restore', { payment_method: id }), {}, {
                    preserveScroll: true,
                });
            }
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.payment-methods.index')} className="text-primary hover:underline">
                        Payment Methods
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Trashed</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <div>
                            <h5 className="font-semibold text-lg dark:text-white-light">Trashed Payment Methods</h5>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {methods.length} trashed {methods.length === 1 ? 'item' : 'items'}
                            </p>
                        </div>
                        <Link href={route('siteadmin.payment-methods.index')} className="btn btn-outline-primary">
                            Back to Payment Methods
                        </Link>
                    </div>

                    {methods.length === 0 ? (
                        <div className="text-center py-8">
                            <div className="text-gray-500 dark:text-gray-400">
                                <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                <p className="text-lg font-medium">No trashed payment methods found</p>
                                <p className="text-sm">All payment methods are active or have been permanently deleted.</p>
                            </div>
                        </div>
                    ) : (
                        <div className="table-responsive">
                            <table className="table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Mode</th>
                                        <th>Status</th>
                                        <th>Deleted At</th>
                                        <th className="!text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {methods.map((method) => (
                                        <tr key={method.id} className="opacity-75">
                                            <td>{method.id}</td>
                                            <td>{method.name}</td>
                                            <td>{method.type}</td>
                                            <td>
                                                <span className={`badge badge-outline-${method.mode === 'live' ? 'success' : 'warning'}`}>
                                                    {method.mode === 'live' ? 'Live' : 'Test'}
                                                </span>
                                            </td>
                                            <td>
                                                <span className={`badge badge-outline-${method.status === 'active' ? 'success' : 'danger'}`}>
                                                    {method.status === 'active' ? 'Active' : 'Deactive'}
                                                </span>
                                            </td>
                                            <td>{formatDate(method.deleted_at)}</td>
                                            <td className="text-center">
                                                <button
                                                    type="button"
                                                    className="btn btn-sm btn-outline-success"
                                                    onClick={() => handleRestore(method.id, method.name)}
                                                >
                                                    Restore
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Trashed;
