<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_usages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('plan_id')->constrained('plans')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained('branches')->onDelete('cascade');
            $table->string('currency_symbol', 8)->default('₹');
            $table->string('currency_text', 8)->default('INR');
            $table->timestamp('purchased_at');
            $table->timestamp('expires_at');
            $table->enum('status', ['pending', 'active', 'expired', 'cancelled'])->default('pending');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('plan_service_usages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('plan_usage_id')->constrained('plan_usages')->onDelete('cascade');
            $table->foreignId('service_id')->constrained('services')->onDelete('cascade');
            $table->string('service_name', 255);
            $table->integer('used_count')->default(0);
            $table->integer('remaining_count');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('plan_service_usages');
        Schema::dropIfExists('plan_usages');
        Schema::enableForeignKeyConstraints();
    }
};
