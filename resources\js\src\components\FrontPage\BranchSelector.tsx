import React from 'react';

interface Branch {
    id: number;
    name: string;
    address: string;
}

interface Tenant {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    logo: string;
    branches: Branch[];
}

interface BranchSelectorProps {
    tenant: Tenant;
    selectedBranch: number;
    handleBranchChange: (branchId: number) => void;
}

const BranchSelector: React.FC<BranchSelectorProps> = ({
    tenant,
    selectedBranch,
    handleBranchChange
}) => {
    if (tenant.branches.length <= 1) {
        return null;
    }

    return (
        <div className="top-0 z-40 sticky bg-white/80 backdrop-blur-xl border-b border-gray-200 w-full">
            <div className="flex flex-row justify-between items-center gap-4 px-4 sm:px-8 py-2 w-full min-h-20">
                {/* Logo on the left */}
                <div className="flex flex-shrink-0 items-center">
                    {tenant.logo && (
                        <div className="group relative">
                            <img
                                src={tenant.logo}
                                alt={tenant.name}
                                className="relative w-14 transform transition-all duration-500 group-hover:scale-105"
                            />
                        </div>
                    )}
                </div>
                {/* Branch select on the right */}
                <div className="flex flex-shrink-0 items-center">
                    <label className="sm:block hidden mr-2 mb-0 font-semibold text-gray-800">Select Branch</label>
                    <select
                        value={selectedBranch}
                        onChange={(e) => handleBranchChange(Number(e.target.value))}
                        className="mr-12 px-4 py-3 border border-gray-300 rounded-lg w-40 sm:w-56 font-medium text-sm text-gray-800 focus:border-orange-800 focus:ring-orange-800"
                    >
                        {tenant.branches.map((branch) => (
                            <option key={branch.id} value={branch.id}>
                                {branch.name}
                            </option>
                        ))}
                    </select>
                </div>
            </div>
        </div>
    );
};

export default BranchSelector;
