<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

final class EmailVerificationController extends Controller
{
    /**
     * Show the email verification notice page.
     */
    public function notice(Request $request): Response
    {
        return Inertia::render('Authentication/VerifyEmail', [
            'status' => $request->session()->get('status'),
            'email' => $request->session()->get('email', ''),
            'layout'           => 'blank',
        ]);
    }

    /**
     * Handle email verification.
     */
    public function verify(Request $request, string $token): RedirectResponse
    {
        $email = $request->query('email');
        
        if (!$email) {
            return redirect()->route('login')->with('error', 'Invalid verification link.');
        }

        // Decode and validate the token
        $decodedToken = base64_decode($token);
        $tokenParts = explode('|', $decodedToken);
        
        if (count($tokenParts) !== 3) {
            return redirect()->route('login')->with('error', 'Invalid verification link.');
        }

        [$tokenEmail, $timestamp, $key] = $tokenParts;
        
        // Verify the token components
        if ($tokenEmail !== $email || $key !== config('app.key')) {
            return redirect()->route('login')->with('error', 'Invalid verification link.');
        }

        // Check if token is not too old (24 hours)
        if (time() - (int)$timestamp > 86400) {
            return redirect()->route('login')->with('error', 'Verification link has expired. Please register again.');
        }

        // Find the user
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            return redirect()->route('login')->with('error', 'User not found.');
        }

        // Check if already verified
        if ($user->hasVerifiedEmail()) {
            return redirect()->route('login')->with('status', 'Email already verified. You can now log in.');
        }

        // Verify the email
        $user->markEmailAsVerified();

        // Determine redirect based on user role
        if ($user->hasRole('vendor')) {
            // For vendors, redirect to their tenant domain
            $centralDomain = config('services.central_domain.url');
            $protocol = $request->isSecure() ? 'https' : 'http';
            $redirectUrl = $protocol . '://' . $user->company_domain . '.' . $centralDomain . '/auth/login';
            
            return redirect()->away($redirectUrl)->with('status', 'Email verified successfully! You can now log in.');
        }

        // For customers, redirect to central login
        return redirect()->route('login')->with('status', 'Email verified successfully! You can now log in.');
    }

    /**
     * Resend verification email.
     */
    public function resend(Request $request): RedirectResponse
    {
        $request->validate([
            'email' => 'required|email|exists:users,email'
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return back()->with('error', 'User not found.');
        }

        if ($user->hasVerifiedEmail()) {
            return back()->with('status', 'Email is already verified.');
        }

        // Send verification email using defer
        defer(function () use ($user) {
            try {
                \App\Services\Notification\NotificationService::sendMail(
                    $user->email,
                    new \App\Mail\EmailVerificationMail($user)
                );
            } catch (\Exception $e) {
                Log::error('Failed to send verification email: ' . $e->getMessage());
            }
        });

        return back()->with('status', 'Verification email sent!');
    }
}
