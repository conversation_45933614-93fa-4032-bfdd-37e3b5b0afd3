<?php

declare(strict_types=1);

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Plan;
use App\Models\PlanUsage;
use App\Models\Service;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

final class PlanController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = Auth::user()->current_branch_id;

        $query = Plan::where('branch_id', $branchId);

        // Apply search filter
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%")
                ->orWhere('description', 'like', "%{$request->search}%");
        }

        // Apply status filter
        if ($request->status !== null) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'name';
        $sortDirection = $request->direction ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        $plans = $query->with(['services'])->paginate(10)
            ->withQueryString();

        return Inertia::render('Vendor/Plan/Index', [
            'plans'   => $plans,
            'filters' => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create(): Response
    {
        $branchId = Auth::user()->current_branch_id;
        $services = Service::where('branch_id', $branchId)
            ->where('is_active', true)
            ->get();

        return Inertia::render('Vendor/Plan/Create', [
            'services' => $services,
        ]);
    }

    public function store(Request $request)
    {
        $user     = auth()->user();
        $branchId = $user->current_branch_id;

        // Check if user has a current branch
        if (! $branchId) {
            return redirect()
                ->back()
                ->withErrors(['branch_id' => 'You must have a current branch set to create plans. Please contact support.'])
                ->withInput();
        }

        $validated = $request->validate([
            'name'                     => 'required|string|max:255',
            'description'              => 'nullable|string|max:1000',
            'price'                    => 'required|numeric|min:1',
            'validity_days'            => 'required|integer|min:1',
            'status'                   => 'required|in:active,inactive',
            'services'                 => 'required|array|min:1',
            'services.*.service_id'    => 'required|exists:services,id',
            'services.*.allowed_count' => 'required|integer|min:1',
        ]);

        try {
            $plan = Plan::create([
                'branch_id'     => $branchId,
                'name'          => $validated['name'],
                'description'   => $validated['description'],
                'price'         => $validated['price'],
                'validity_days' => $validated['validity_days'],
                'status'        => $validated['status'],
            ]);

            // Attach services with their allowed counts
            foreach ($validated['services'] as $service) {
                $plan->services()->attach($service['service_id'], [
                    'allowed_count' => $service['allowed_count'],
                ]);
            }

            return redirect()
                ->route('vendor.plans.index')
                ->with('success', 'Plan created successfully');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to create plan: '.$e->getMessage()])
                ->withInput();
        }
    }

    public function edit(Plan $plan): Response
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the plan belongs to the current branch
        if ($plan->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $services = Service::where('branch_id', $branchId)
            ->where('is_active', true)
            ->get();

        // Load the plan with its services and their pivot data
        $plan->load(['services' => function ($query) {
            $query->withPivot('allowed_count');
        }]);

        return Inertia::render('Vendor/Plan/Edit', [
            'plan'     => $plan,
            'services' => $services,
        ]);
    }

    public function update(Request $request, Plan $plan)
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the plan belongs to the current branch
        if ($plan->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name'                     => 'required|string|max:255',
            'description'              => 'nullable|string|max:1000',
            'price'                    => 'required|numeric|min:0',
            'validity_days'            => 'required|integer|min:1',
            'status'                   => 'required|in:active,inactive',
            'services'                 => 'required|array|min:1',
            'services.*.service_id'    => 'required|exists:services,id',
            'services.*.allowed_count' => 'required|integer|min:1',
        ]);

        try {
            $plan->update([
                'name'          => $validated['name'],
                'description'   => $validated['description'],
                'price'         => $validated['price'],
                'validity_days' => $validated['validity_days'],
                'status'        => $validated['status'],
            ]);

            // Sync services with their allowed counts
            $syncData = [];
            foreach ($validated['services'] as $service) {
                $syncData[$service['service_id']] = [
                    'allowed_count' => $service['allowed_count'],
                ];
            }
            $plan->services()->sync($syncData);

            return redirect()
                ->route('vendor.plans.index')
                ->with('success', 'Plan updated successfully');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to update plan: '.$e->getMessage()])
                ->withInput();
        }
    }

    public function destroy(Plan $plan)
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the plan belongs to the current branch
        if ($plan->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        try {
            $plan->delete();

            return redirect()
                ->route('vendor.plans.index')
                ->with('success', 'Plan deleted successfully');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to delete plan: '.$e->getMessage()]);
        }
    }

    public function updateStatus(Request $request, Plan $plan)
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the plan belongs to the current branch
        if ($plan->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $plan->update(['status' => $validated['status']]);

            return redirect()
                ->route('vendor.plans.index')
                ->with('success', 'Plan status updated successfully');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to update plan status: '.$e->getMessage()]);
        }
    }

    public function updatePlanStatus(Request $request, PlanUsage $planUsage)
    {
        try {
            $validated = $request->validate([
                'status' => 'required|in:pending,active,expired,cancelled',
            ]);

            DB::beginTransaction();

            $planUsage->status = $validated['status'];

            // If status is being changed to active, update the expiration date
            if ($validated['status'] === 'active' && $planUsage->status !== 'active') {
                $planUsage->expires_at = now()->addDays($planUsage->plan->validity_days);
            }

            $planUsage->save();

            DB::commit();

            return back()->with('success', 'Plan status updated successfully!');

        } catch (\Exception $e) {
            DB::rollBack();

            return back()->withErrors(['error' => 'Failed to update plan status: '.$e->getMessage()]);
        }
    }

    public function purchases(Request $request)
    {
        $user     = auth()->user();
        $branchId = Auth::user()->current_branch_id;

        $query = PlanUsage::with(['user', 'plan', 'planServiceUsages.service'])
            ->where('branch_id', $branchId);

        // Optional: Add filters for search, status, etc.
        if ($request->search) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('name', 'like', '%'.$request->search.'%')
                    ->orWhere('email', 'like', '%'.$request->search.'%')
                    ->orWhere('phone', 'like', '%'.$request->search.'%');
            });
        }
        if ($request->status) {
            $query->where('status', $request->status);
        }

        // Default sorting by created_at
        $query->orderBy('created_at', 'desc');

        $purchases = $query->paginate(15)->withQueryString();

        // Transform the data
        $transformedData = $purchases->getCollection()->map(function ($usage) {
            return [
                'id'   => $usage->id,
                'user' => [
                    'id'    => $usage->user->id,
                    'name'  => $usage->user->name,
                    'email' => $usage->user->email,
                    'phone' => $usage->user->phone,
                ],
                'plan' => [
                    'id'            => $usage->plan->id,
                    'name'          => $usage->plan->name,
                    'price'         => $usage->plan->price,
                    'validity_days' => $usage->plan->validity_days,
                ],
                'created_at'        => $usage->created_at,
                'purchased_at'      => $usage->purchased_at,
                'currency_symbol'   => $usage->currency_symbol,
                'currency_text'     => $usage->currency_text,
                'expires_at'        => $usage->expires_at,
                'status'            => $usage->status,
                'planServiceUsages' => $usage->planServiceUsages->map(function ($serviceUsage) {
                    return [
                        'id'      => $serviceUsage->id,
                        'service' => [
                            'id'   => $serviceUsage->service->id,
                            'name' => ($serviceUsage->service_name) ? $serviceUsage->service_name : $serviceUsage->service->name,
                        ],
                        'remaining_count' => $serviceUsage->remaining_count,
                        'used_count'      => $serviceUsage->used_count,
                    ];
                }),
            ];
        });

        // Set the transformed collection back to the paginator
        $purchases->setCollection($transformedData);

        return inertia('Vendor/Plans/Purchases', [
            'purchases' => [
                'data'  => $purchases->items(),
                'links' => $purchases->linkCollection(),
            ],
            'filters' => [
                'search'    => $request->search ?? '',
                'status'    => $request->status ?? '',
                'sort'      => 'created_at',
                'direction' => 'desc',
            ],
        ]);
    }
}
