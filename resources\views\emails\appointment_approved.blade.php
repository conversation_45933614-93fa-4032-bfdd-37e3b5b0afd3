@php
    $staff = $staff ?? null;
@endphp
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Your Appointment is Approved!</title>
</head>
<body style="font-family: Arial, sans-serif; background: #f7f7f7; margin: 0; padding: 0;">
    <table width="100%" bgcolor="#f7f7f7" cellpadding="0" cellspacing="0" style="padding: 40px 0;">
        <tr>
            <td align="center">
                <table width="600" bgcolor="#fff" cellpadding="0" cellspacing="0" style="border-radius: 12px; box-shadow: 0 4px 24px rgba(7,30,59,0.10); overflow: hidden;">
                    <tr>
                        <td style="background: #FEBA00; color: #071E3B; padding: 32px 32px 20px 32px; text-align: center;">
                            <h1 style="margin: 0; font-size: 30px; letter-spacing: 1px; font-weight: 700; color: #071E3B;">
                                Appointment Approved!
                            </h1>
                            <p style="margin: 0; font-size: 16px; color: #071E3B; opacity: 0.85; font-weight: 400;">
                                Your appointment has been approved. Please find the details below.
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 32px;">
                            <div style="background: #FEBA00; color: #071E3B; border-radius: 8px; padding: 18px 24px; margin-bottom: 28px; text-align: center;">
                                <span style="font-size: 18px; font-weight: 600; letter-spacing: 1px;">Ticket Number: {{ $appointment->ticket_number }}</span>
                            </div>
                            <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 24px;">
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #071E3B;">Date:</td>
                                    <td style="padding: 8px 0;">{{ \Carbon\Carbon::parse($appointment->appointment_date)->format('M d, Y') }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #071E3B;">Time:</td>
                                    <td style="padding: 8px 0;">{{ date('h:i A', strtotime($appointment->appointment_time)) }}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #071E3B;">Branch:</td>
                                    <td style="padding: 8px 0;">{{ $branch->name }}<br><span style="color:#888; font-size:13px;">{{ $branch->address }}</span></td>
                                </tr>
                                @if($appointment->notes)
                                <tr>
                                    <td style="padding: 8px 0; font-weight: bold; color: #071E3B;">Notes:</td>
                                    <td style="padding: 8px 0;">{{ $appointment->notes }}</td>
                                </tr>
                                @endif
                            </table>
                            <h3 style="color: #071E3B; margin-bottom: 8px; font-size: 18px; font-weight: 700;">Services Booked</h3>
                            <ul style="padding-left: 20px; margin-bottom: 24px;">
                                @foreach($appointment->services as $service)
                                    <li style="margin-bottom: 6px; color: #333; font-size: 15px;">
                                        <span style="font-weight: 600; color: #071E3B;">{{ $service->name }}</span>
                                        <span style="color: #FEBA00; font-weight: 700;">({{ $appointment->currency_symbol }} {{ $service->price }})</span>
                                    </li>
                                @endforeach
                            </ul>
                            @if($staff)
                                <h3 style="color: #071E3B; margin-bottom: 8px; font-size: 18px; font-weight: 700;">Assigned Staff</h3>
                                <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 24px;">
                                    <tr>
                                        <td style="padding: 6px 0; font-weight: bold; color: #071E3B;">Name:</td>
                                        <td style="padding: 6px 0;">{{ $staff->name }}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 6px 0; font-weight: bold; color: #071E3B;">Email:</td>
                                        <td style="padding: 6px 0;">{{ $staff->email }}</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 6px 0; font-weight: bold; color: #071E3B;">Phone:</td>
                                        <td style="padding: 6px 0;">{{ $staff->phone }}</td>
                                    </tr>
                                </table>
                            @endif
                            <div style="margin-top: 32px; text-align: center;">
                                @php
                                    $centralDomain = rtrim(config('services.central_domain.url'), '/');
                                    $dashboardPath = route('user.dashboard', [], false);
                                    $centralDashboardUrl = $centralDomain . $dashboardPath;
                                @endphp
                                <a href="{{ $centralDashboardUrl }}" style="background: #FEBA00; color: #071E3B; padding: 14px 36px; border-radius: 6px; text-decoration: none; font-size: 17px; font-weight: 700; box-shadow: 0 2px 8px rgba(7,30,59,0.08); display: inline-block;">View Appointment</a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="background: #071E3B; color: #fff; text-align: center; padding: 18px 16px 8px 16px; font-size: 13px;">
                            &copy; {{ date('Y') }} Salozy CRM. All rights reserved.<br>
                            <span style="color: #FEBA00; font-weight: 600;">Powered by <a href="https://astridtechnology.com/" style="color: #FEBA00; text-decoration: underline;" target="_blank">Astrid Technology</a></span>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html> 