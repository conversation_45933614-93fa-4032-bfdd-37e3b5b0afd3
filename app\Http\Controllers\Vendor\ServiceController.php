<?php

declare(strict_types=1);

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

final class ServiceController extends Controller
{
    public function index(Request $request): Response
    {
        $branchId = Auth::user()->current_branch_id;

        $query = Service::where('branch_id', $branchId);

        // Apply search filter
        if ($request->search) {
            $query->where('name', 'like', "%{$request->search}%")
                ->orWhere('description', 'like', "%{$request->search}%");
        }

        // Apply status filter
        if ($request->status !== null) {
            $query->where('is_active', $request->status === 'active');
        }

        // Apply sorting
        $sortField     = $request->sort      ?? 'name';
        $sortDirection = $request->direction ?? 'asc';
        $query->orderBy($sortField, $sortDirection);

        $services = $query->paginate(10)
            ->withQueryString();

        return Inertia::render('Vendor/Service/Index', [
            'services' => $services,
            'filters'  => $request->only(['search', 'status', 'sort', 'direction']),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('Vendor/Service/AddService');
    }

    public function store(Request $request)
    {
        $user     = auth()->user();
        $branchId = $user->current_branch_id;

        // Check if user has a current branch
        if (! $branchId) {
            return redirect()
                ->back()
                ->withErrors(['branch_id' => 'You must have a current branch set to create services. Please contact support.'])
                ->withInput();
        }

        $validated = $request->validate([
            'name'                   => 'required|string|max:255',
            'duration_minutes'       => 'required|integer|min:5|max:480',
            'price'                  => 'required|numeric|min:1',
            'is_active'              => 'boolean',
            'gender'                 => 'required|in:male,female',
            'reminder_after_service' => 'required|integer|min:0',
            'total_repeat_service'   => 'required_if:reminder_after_service,>,0|integer|min:1',
        ]);

        $validated['branch_id']              = $branchId;
        $validated['is_active']              = $validated['is_active'] ?? true;
        $validated['description']            = $request->input('description', '');
        $validated['gender']                 = $request->input('gender');
        $validated['reminder_after_service'] = $request->input('reminder_after_service');
        $validated['total_repeat_service']   = $request->input('total_repeat_service', 0);

        try {
            Service::create($validated);

            return redirect()
                ->route('vendor.services.index')
                ->with('success', 'Service created successfully');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => 'Failed to create service: '.$e->getMessage()])
                ->withInput();
        }
    }

    public function edit(Service $service): Response
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the service belongs to the current branch
        if ($service->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        return Inertia::render('Vendor/Service/EditService', [
            'service' => $service,
        ]);
    }

    public function update(Request $request, Service $service)
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the service belongs to the current branch
        if ($service->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'name'                   => 'required|string|max:255',
            'duration_minutes'       => 'required|integer|min:5|max:480',
            'price'                  => 'required|numeric|min:1',
            'is_active'              => 'boolean',
            'gender'                 => 'required|in:male,female',
            'reminder_after_service' => 'required|integer|min:0',
            'total_repeat_service'   => 'required_if:reminder_after_service,>,0|integer|min:1',
        ]);

        $validated['is_active']              = $validated['is_active'] ?? true;
        $validated['description']            = $request->input('description', '');
        $validated['gender']                 = $request->input('gender');
        $validated['reminder_after_service'] = $request->input('reminder_after_service');
        $validated['total_repeat_service']   = $request->input('total_repeat_service', 0);

        $service->update($validated);

        return redirect()
            ->route('vendor.services.index')
            ->with('success', 'Service updated successfully');
    }

    public function destroy(Service $service)
    {
        $branchId = Auth::user()->current_branch_id;

        // Ensure the service belongs to the current branch
        if ($service->branch_id !== $branchId) {
            abort(403, 'Unauthorized action.');
        }

        // Check if service is used in any appointments
        if ($service->appointments()->count() > 0) {
            return redirect()
                ->route('vendor.services.index')
                ->with('error', 'Cannot delete a service that is used in appointments');
        }

        $service->delete();

        return redirect()
            ->route('vendor.services.index')
            ->with('success', 'Service deleted successfully');
    }
}
