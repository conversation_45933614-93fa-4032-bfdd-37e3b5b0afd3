import React, { useEffect, useState } from 'react';
import { Link, useForm } from '@inertiajs/react';
import Swal from 'sweetalert2';
import { useRoute } from '@hooks/useRoute';

const Create = ({ flash }: { flash?: { success?: string; error?: string } }) => {
    const { route } = useRoute();
    const [config, setConfig] = useState<{ key: string; value: string }[]>([
        { key: '', value: '' },
    ]);
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        driver: '',
        is_active: true,
        config: {},
    });

    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
            reset();
            setConfig([{ key: '', value: '' }]);
        }
    }, [flash?.error, flash?.success, reset]);

    useEffect(() => {
        const configObj: Record<string, string> = {};
        for (const pair of config) {
            if (pair.key) configObj[pair.key] = pair.value;
        }
        setData('config', configObj);
    }, [config]);

    const handleConfigChange = (idx: number, field: 'key' | 'value', value: string) => {
        setConfig((prev) => {
            const updated = [...prev];
            updated[idx][field] = value;
            return updated;
        });
    };

    const addConfig = () => {
        setConfig((prev) => [...prev, { key: '', value: '' }]);
    };

    const removeConfig = (idx: number) => {
        setConfig((prev) => prev.filter((_, i) => i !== idx));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const configObj: Record<string, string> = {};
        for (const pair of config) {
            if (pair.key.trim()) configObj[pair.key.trim()] = pair.value;
        }
        if (Object.keys(configObj).length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Please add at least one key-value pair in config.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
            return;
        }
        setData('config', configObj);
        post(route('siteadmin.notification-channels.store'));
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.notification-channels.index')} className="text-primary hover:underline">
                        Notification Channels
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Add</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Add Notification Channel</h5>
                    </div>
                    <form className="space-y-5" onSubmit={handleSubmit} autoComplete="off">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="name">Name <span className="text-danger">*</span></label>
                                <input id="name" type="text" className="form-input" value={data.name} onChange={e => setData('name', e.target.value)} required />
                                {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                            </div>
                            <div>
                                <label htmlFor="driver">Driver <span className="text-danger">*</span></label>
                                <input id="driver" type="text" className="form-input" value={data.driver} onChange={e => setData('driver', e.target.value)} required />
                                {errors.driver && <div className="text-danger mt-1">{errors.driver}</div>}
                            </div>
                            <div>
                                <label htmlFor="is_active">Status <span className="text-danger">*</span></label>
                                <select id="is_active" className="form-input" value={data.is_active ? 'true' : 'false'} onChange={e => setData('is_active', e.target.value === 'true')} required>
                                    <option value="true">Active</option>
                                    <option value="false">Inactive</option>
                                </select>
                                {errors.is_active && <div className="text-danger mt-1">{errors.is_active}</div>}
                            </div>
                        </div>
                        <div>
                            <label className="block font-semibold mb-2">Config (Key-Value Pairs) <span className="text-danger">*</span></label>
                            <div className="space-y-2">
                                {config.map((pair, idx) => (
                                    <div key={idx} className="flex gap-2 items-center">
                                        <input
                                            type="text"
                                            className="form-input flex-1"
                                            placeholder="Key"
                                            value={pair.key}
                                            onChange={e => handleConfigChange(idx, 'key', e.target.value)}
                                            required
                                        />
                                        <input
                                            type="text"
                                            className="form-input flex-1"
                                            placeholder="Value"
                                            value={pair.value}
                                            onChange={e => handleConfigChange(idx, 'value', e.target.value)}
                                            required
                                        />
                                        {config.length > 1 && (
                                            <button type="button" className="btn btn-sm btn-outline-danger" onClick={() => removeConfig(idx)}>
                                                Remove
                                            </button>
                                        )}
                                    </div>
                                ))}
                                <button type="button" className="btn btn-sm btn-outline-primary mt-2" onClick={addConfig}>
                                    + Add Key-Value
                                </button>
                            </div>
                        </div>
                        <div className="flex items-center justify-end gap-4 mt-4">
                            <Link href={route('siteadmin.notification-channels.index')} className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary" disabled={processing}>
                                {processing ? 'Saving...' : 'Add Notification Channel'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Create;
