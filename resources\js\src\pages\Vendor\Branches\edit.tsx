import React, { useEffect, useState } from 'react';
import { Link, useForm } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import { FaFacebook, FaInstagram, FaWhatsapp, FaLinkedin, FaYoutube, FaTiktok, FaPinterest, FaTelegram, FaGlobe } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';

interface Props {
    branch: {
        id: number;
        name: string;
        address: string;
        phone: string;
        email: string | null;
        is_active: boolean;
        allow_staff: boolean;
        social_links?: { icon: string; url: string }[];
        currency_symbol?: string;
        new_password: string;
        currency_text?: string;
    };
}

const SOCIAL_ICONS = [
    { label: 'Facebook', value: 'facebook', Icon: FaFacebook },
    { label: 'Instagram', value: 'instagram', Icon: FaInstagram },
    { label: 'X (Twitter)', value: 'x', Icon: FaXTwitter },
    { label: 'WhatsApp', value: 'whatsapp', Icon: FaWhatsapp },
    { label: 'LinkedIn', value: 'linkedin', Icon: FaLinkedin },
    { label: 'YouTube', value: 'youtube', Icon: FaYoutube },
    { label: 'TikTok', value: 'tiktok', Icon: FaTiktok },
    { label: 'Pinterest', value: 'pinterest', Icon: FaPinterest },
    { label: 'Telegram', value: 'telegram', Icon: FaTelegram },
    { label: 'Website', value: 'website', Icon: FaGlobe },
];

const Edit = ({ branch }: Props) => {
    const dispatch = useDispatch();
    const { data, setData, put, processing, errors } = useForm<{
        name: string;
        address: string;
        phone: string;
        email: string;
        is_active: boolean;
        allow_staff: boolean;
        social_links: { icon: string; url: string }[];
        currency_symbol?: string;
        currency_text?: string;
        new_password?: string;
    }>({
        name: branch.name,
        address: branch.address,
        phone: branch.phone,
        email: branch.email || '',
        is_active: branch.is_active,
        allow_staff: branch.allow_staff ?? false,
        social_links: branch.social_links || [],
        currency_symbol: branch.currency_symbol || '₹',
        currency_text: branch.currency_text || 'INR',
        new_password: '',
    });
    const [socialLinks, setSocialLinks] = useState<{ icon: string; url: string }[]>(branch.social_links && branch.social_links.length > 0 ? branch.social_links : [{ icon: '', url: '' }]);
    const [changePassword, setChangePassword] = useState(false);

    // Keep data.social_links in sync with local state
    useEffect(() => {
        setData('social_links', socialLinks.filter(link => link.icon && link.url));
    }, [socialLinks]);

    useEffect(() => {
        dispatch(setPageTitle('Edit Branch'));
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const payload: any = {
            ...data,
            social_links: socialLinks.filter(link => link.icon && link.url),
        };
        if (!changePassword) {
            payload.new_password = '';
        }
        put(`/vendor/branches/${branch.id}`, payload);
    };

    const handleChangePassword = (checked: boolean) => {
        setChangePassword(checked);
        if (!checked) {
            setData('new_password', null as any);
        }
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/branches" className="text-primary hover:underline">
                        Branches
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Edit Branch</h5>
                    </div>

                    <form className="space-y-5" onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="name">Name <span className="text-danger">*</span></label>
                                <input
                                    id="name"
                                    type="text"
                                    className="form-input"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    required
                                />
                                {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                            </div>
                            <div>
                                <label htmlFor="address">Address <span className="text-danger">*</span></label>
                                <input
                                    id="address"
                                    type="text"
                                    className="form-input"
                                    value={data.address}
                                    onChange={(e) => setData('address', e.target.value)}
                                    required
                                />
                                {errors.address && <div className="text-danger mt-1">{errors.address}</div>}
                            </div>
                            <div>
                                <label htmlFor="phone">Phone <span className="text-danger">*</span></label>
                                <input
                                    id="phone"
                                    type="text"
                                    className="form-input"
                                    value={data.phone}
                                    onChange={(e) => setData('phone', e.target.value)}
                                    required
                                />
                                {errors.phone && <div className="text-danger mt-1">{errors.phone}</div>}
                            </div>
                            <div>
                                <label htmlFor="email">Branch User Email <span className="text-danger">*</span></label>
                                <input
                                    id="email"
                                    type="email"
                                    className="form-input"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    required
                                />
                                {errors.email && <div className="text-danger mt-1">{errors.email}</div>}
                            </div>
                            <div className="flex items-center mt-2">
                                <input
                                    id="changePassword"
                                    type="checkbox"
                                    className="form-checkbox mr-2"
                                    checked={changePassword}
                                    onChange={e => handleChangePassword(e.target.checked)}
                                />
                                <label htmlFor="changePassword">Change Branch User Password?</label>
                            </div>
                            {changePassword && (
                                <div>
                                    <label htmlFor="password">New Password <span className="text-danger">*</span></label>
                                    <input
                                        id="password"
                                        type="password"
                                        className="form-input"
                                        value={data.new_password}
                                        onChange={e => setData('new_password', e.target.value)}
                                        required={changePassword}
                                    />
                                    {errors.new_password && <div className="text-danger mt-1">{errors.new_password}</div>}
                                </div>
                            )}
                            <div className="md:col-span-2 flex gap-4">
                                <div className="w-1/2">
                                    <label htmlFor="currency_symbol">Currency Symbol <span className="text-danger">*</span></label>
                                    <input
                                        id="currency_symbol"
                                        type="text"
                                        className="form-input"
                                        value={data.currency_symbol}
                                        onChange={(e) => setData('currency_symbol', e.target.value)}
                                        required
                                        maxLength={8}
                                    />
                                    {errors.currency_symbol && <div className="text-danger mt-1">{errors.currency_symbol}</div>}
                                </div>
                                <div className="w-1/2">
                                    <label htmlFor="currency_text">Currency Text <span className="text-danger">*</span></label>
                                    <input
                                        id="currency_text"
                                        type="text"
                                        className="form-input"
                                        value={data.currency_text}
                                        onChange={(e) => setData('currency_text', e.target.value)}
                                        required
                                        maxLength={8}
                                    />
                                    {errors.currency_text && <div className="text-danger mt-1">{errors.currency_text}</div>}
                                </div>
                            </div>
                            <div className="md:col-span-2 flex items-center mt-6">
                                <input
                                    type="checkbox"
                                    className="form-checkbox mr-2"
                                    checked={data.is_active}
                                    onChange={(e) => setData('is_active', e.target.checked)}
                                />
                                <label>Active</label>
                            </div>
                            <div className="md:col-span-2 flex items-center mt-2">
                                <input
                                    type="checkbox"
                                    className="form-checkbox mr-2"
                                    checked={data.allow_staff}
                                    onChange={(e) => setData('allow_staff', e.target.checked)}
                                />
                                <label>Allow user to select staff for appointment</label>
                            </div>
                        </div>
                        <div>
                            <label>Social Links (max 10)</label>
                            {socialLinks.map((link, idx) => {
                                const IconComponent = SOCIAL_ICONS.find(i => i.value === link.icon)?.Icon;
                                return (
                                    <div key={idx} className="flex gap-2 mb-2 items-center">
                                        <select
                                            className="form-input w-1/3"
                                            value={link.icon}
                                            onChange={e => {
                                                const updated = [...socialLinks];
                                                updated[idx].icon = e.target.value;
                                                setSocialLinks(updated);
                                            }}
                                        >
                                            <option value="">Select Icon</option>
                                            {SOCIAL_ICONS.map(opt => (
                                                <option key={opt.value} value={opt.value}>{opt.label}</option>
                                            ))}
                                        </select>
                                        <input
                                            type="url"
                                            className="form-input w-2/3"
                                            placeholder="URL (https://...)"
                                            value={link.url}
                                            onChange={e => {
                                                const updated = [...socialLinks];
                                                updated[idx].url = e.target.value;
                                                setSocialLinks(updated);
                                            }}
                                        />
                                        <button type="button" className="btn btn-danger px-2" onClick={() => setSocialLinks(socialLinks.filter((_, i) => i !== idx))} disabled={socialLinks.length === 1}>Remove</button>
                                        {IconComponent && <span className="ml-2"><IconComponent size={20} /></span>}
                                    </div>
                                );
                            })}
                            <button type="button" className="btn btn-primary mt-2" onClick={() => socialLinks.length < 10 && setSocialLinks([...socialLinks, { icon: '', url: '' }])} disabled={socialLinks.length >= 10}>Add Social Link</button>
                            {errors.social_links && <div className="text-danger mt-1">{errors.social_links}</div>}
                        </div>
                        <div className="flex items-center justify-end gap-4 mt-4">
                            <Link href="/vendor/branches" className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary" disabled={processing}>
                                {processing ? 'Saving...' : 'Save Changes'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Edit; 