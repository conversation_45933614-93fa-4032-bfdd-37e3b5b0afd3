<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->string('currency_symbol', 8)->default('₹');
            $table->string('currency_code', 8)->default('INR');
            $table->enum('billing_cycle', ['monthly', 'yearly']);

            // Limits
            $table->integer('max_services')->default(0); // 0 = unlimited
            $table->integer('max_appointments_per_month')->default(0); // 0 = unlimited
            $table->integer('max_seats')->default(0); // 0 = unlimited
            $table->integer('max_branches')->default(1);
            $table->integer('max_staff')->default(0); // 0 = unlimited

            // Features
            $table->boolean('has_analytics')->default(false);
            $table->boolean('has_api_access')->default(false);
            $table->boolean('has_custom_branding')->default(false);
            $table->boolean('has_priority_support')->default(false);

            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
