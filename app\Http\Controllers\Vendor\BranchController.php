<?php

declare(strict_types=1);

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\User;
use App\Services\BranchService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\Permission\Models\Role;

final class BranchController extends Controller
{
    public function __construct(
        private readonly BranchService $branchService,
    ) {}

    public function index(Request $request): Response
    {
        $userId          = auth()->id();
        $currentBranchId = Auth::user()->current_branch_id;

        $branches = $this->branchService->getBranchesForUser($userId, $request->all());

        return Inertia::render('Vendor/Branches/index', [
            'branches'          => $branches,
            'filters'           => $request->only(['search', 'status', 'sort', 'direction']),
            'current_branch_id' => $currentBranchId,
            'flash'             => [
                'success' => session('success'),
                'error'   => session('error'),
            ],
        ]);
    }

    public function updateCurrentBranch(Request $request)
    {
        $userId   = auth()->id();
        $branchId = $request->input('branch_id');

        $this->branchService->updateCurrentBranch($userId, $branchId);

        return redirect()->route('vendor.branches.index');
    }

    public function create(): Response
    {
        return Inertia::render('Vendor/Branches/create');
    }

    public function store(Request $request)
    {
        $userId     = auth()->id();
        $parentUser = User::findOrFail($userId);

        $validated = $request->validate([
            'name'                => 'required|string|max:255',
            'address'             => 'required|string|max:500',
            'phone'               => 'required|string|max:20',
            'email'               => 'required|email|max:255|unique:users,email',
            'password'            => 'required|string|min:6',
            'is_active'           => 'boolean',
            'allow_staff'         => 'boolean',
            'social_links'        => 'nullable|array',
            'social_links.*.icon' => 'required_with:social_links|string',
            'social_links.*.url'  => 'required_with:social_links|url',
            'currency_symbol'     => 'required|string|max:8',
            'currency_text'       => 'required|string|max:8',
        ]);

        // Ensure the 'branchuser' role exists
        $role = Role::firstOrCreate(['name' => 'branchuser']);

        // Create the branch user
        $branchUser = User::create([
            'name'           => $validated['name'],
            'email'          => $validated['email'],
            'tenant_id'      => $parentUser->tenant_id,
            'company_name'   => $parentUser->company_name,
            'password'       => Hash::make($validated['password']),
            'is_active'      => true,
            'parent_user_id' => $userId,
        ]);
        $branchUser->assignRole($role);

        $validated['user_id']        = $userId;
        $validated['is_active']      = $validated['is_active'] ?? true;
        $validated['branch_user_id'] = $branchUser->id;

        $branchData = $this->branchService->createBranch($validated);

        $branchUser->branch_id         = $branchData->id;
        $branchUser->current_branch_id = $branchData->id;
        $branchUser->save();

        return redirect()
            ->route('vendor.branches.index')
            ->with('success', 'Branch and branch user created successfully');
    }

    public function edit(Branch $branch): Response
    {
        return Inertia::render('Vendor/Branches/edit', [
            'branch' => $branch,
        ]);
    }

    public function update(Request $request, Branch $branch)
    {
        $validated = $request->validate([
            'name'                => 'required|string|max:255',
            'address'             => 'required|string|max:500',
            'phone'               => 'required|string|max:20',
            'email'               => 'required|email|max:255|unique:users,email,'.$branch->branch_user_id,
            'is_active'           => 'boolean',
            'allow_staff'         => 'boolean',
            'social_links'        => 'nullable|array',
            'social_links.*.icon' => 'required_with:social_links|string',
            'social_links.*.url'  => 'required_with:social_links|url',
            'currency_symbol'     => 'required|string|max:8',
            'currency_text'       => 'required|string|max:8',
            'new_password'        => 'nullable|string|min:6',
        ]);

        $validated['is_active'] = $validated['is_active'] ?? true;

        $userId   = auth()->id();
        $authUser = User::findOrFail($userId);

        if ($authUser->hasRole('branchuser') && $authUser->id === $branch->branch_user_id) {

            if ($authUser->hasRole('vendor') && $authUser->id === $branch->branch_user_id) {
            } else {
                $branchUser        = User::findOrFail($branch->branch_user_id);
                $branchUser->name  = $validated['name'];
                $branchUser->email = $validated['email'];
                if (! empty($validated['new_password'])) {
                    $branchUser->password = Hash::make($validated['new_password']);
                }
                $branchUser->save();
            }
        }

        $branchUser = User::findOrFail($branch->branch_user_id);
        if (! empty($validated['new_password'])) {
            $branchUser->password = Hash::make($validated['new_password']);
        }
        $branchUser->email = $validated['email'];
        $branchUser->phone = $validated['phone'];
        $branchUser->save();

        $this->branchService->updateBranch($branch, $validated);

        return redirect()
            ->route('vendor.branches.index')
            ->with('success', 'Branch and branch user updated successfully');
    }

    public function destroy(Branch $branch)
    {
        $userId = auth()->id();
        $result = $this->branchService->deleteBranch($userId, $branch);

        return $result;
    }

    public function trashed(Request $request): Response
    {
        $userId   = auth()->id();
        $branches = $this->branchService->getTrashedBranchesForUser($userId, $request->all());

        return Inertia::render('Vendor/Branches/trashed', [
            'branches' => $branches,
            'filters'  => $request->only(['search', 'sort', 'direction']),
        ]);
    }

    public function restore($id)
    {
        $this->branchService->restoreBranch($id);

        return redirect()
            ->route('vendor.branches.trashed')
            ->with('success', 'Branch restored successfully');
    }

    public function forceDelete($id)
    {
        $result = $this->branchService->forceDeleteBranch($id);

        return $result;
    }

    public function updateAllowStaff(Request $request, Branch $branch)
    {
        $validated = $request->validate([
            'allow_staff' => 'required|boolean',
        ]);
        $this->branchService->updateAllowStaff($branch, $validated['allow_staff']);

        return redirect()->route('vendor.branches.index')->with('success', 'Status updated successfully');
    }
}
