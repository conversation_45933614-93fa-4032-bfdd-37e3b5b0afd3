import React from 'react';
import { Link } from '@inertiajs/react';
import { useRouting } from '@hooks/useRouting';
import { useRoute } from '@hooks/useRoute';

interface NavLinkProps {
    href: string;
    routeName?: string;
    params?: any;
    className?: string | ((props: { isActive: boolean }) => string);
    children: React.ReactNode;
    activeClassName?: string;
    exact?: boolean;
}

const NavLink: React.FC<NavLinkProps> = ({
    href,
    routeName,
    params,
    className = '',
    children,
    activeClassName = 'active',
    exact = false
}) => {
    const { isActive } = useRouting();
    const { route } = useRoute();

    // Use the provided href or generate one from route name
    const finalHref = routeName ? route(routeName, params) : href;
    const active = isActive(finalHref, exact);

    const classes = typeof className === 'function'
        ? className({ isActive: active })
        : `${className} ${active ? activeClassName : ''}`;

    return (
        <Link href={finalHref} className={classes.trim()}>
            {children}
        </Link>
    );
};

export default NavLink;




