<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\EmailVerificationMail;
use App\Models\Tenant;
use App\Models\User;
use App\Services\Notification\NotificationService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;
use Stancl\Tenancy\Database\Models\Domain;

final class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('Authentication/RegisterBoxed', [
            'layout' => 'blank',
        ]);
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $centralDomain = config('services.central_domain.url');

        $validationRules = [
            'name'     => 'required|string|max:255',
            'email'    => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role'     => 'required|in:customer,vendor',
        ];

        // Add vendor-specific validation rules
        if ($request->role === 'vendor') {
            $validationRules['company_name']   = 'required|string|max:255';
            $validationRules['company_domain'] = [
                'required',
                'string',
                'max:255',
                'regex:/^[a-z0-9-]+$/', // Only lowercase letters, numbers, and dashes
                'unique:users,company_domain',
                'unique:domains,domain',
            ];
        }

        $validated = $request->validate($validationRules);

        $userData = [
            'name'              => $validated['name'],
            'email'             => $validated['email'],
            'password'          => Hash::make($validated['password']),
            'email_verified_at' => null, // Don't verify email automatically
            'is_active'         => true,
        ];

        // Handle vendor registration
        if ($validated['role'] === 'vendor') {
            // Create tenant first
            $tenant = Tenant::create([
                'id' => $validated['company_domain'],
            ]);

            // Create domain for the tenant
            $domain = Domain::create([
                'domain'    => $validated['company_domain'].'.'.$centralDomain,
                'tenant_id' => $tenant->id,
            ]);

            // Tenant migrations will be run automatically by the TenantCreated event

            // Add vendor-specific data to user
            $userData['company_name']   = $validated['company_name'];
            $userData['company_domain'] = $validated['company_domain'];
            $userData['tenant_id']      = $tenant->id;
        }

        $user = User::create($userData);

        // Assign role
        $user->assignRole($validated['role']);

        // Create default branch for vendor
        if ($validated['role'] === 'vendor') {
            $branch = \App\Models\Branch::create([
                'user_id'        => $user->id,
                'branch_user_id' => $user->id,
                'name'           => $validated['company_name'], // Use company name as branch name
                'email'          => null, // Nullable
                'address'        => null, // Nullable
                'phone'          => null, // Nullable
                'logo'           => null, // Nullable
                'is_active'      => true, // Default active
            ]);

            // Set the vendor's current branch
            $user->update(['current_branch_id' => $branch->id]);

            // Create default trial subscription (14 days trial)
            $starterPlan = \App\Models\SubscriptionPlan::where('name', 'Starter')->first();
            if ($starterPlan) {
                $trialStartsAt = now();
                $trialEndsAt   = $trialStartsAt->copy()->addDays(14);

                \App\Models\VendorSubscription::create([
                    'user_id'                => $user->id,
                    'subscription_plan_id'   => $starterPlan->id,
                    'starts_at'              => $trialStartsAt,
                    'ends_at'                => $trialEndsAt,
                    'trial_ends_at'          => $trialEndsAt,
                    'status'                 => 'trial',
                    'billing_period_start'   => $trialStartsAt->startOfMonth()->toDateString(),
                    'billing_period_end'     => $trialStartsAt->endOfMonth()->toDateString(),
                    'current_branches_count' => 1, // Default branch created above
                ]);
            }
        }

        event(new Registered($user));

        // Send email verification using defer for better user experience
        defer(function () use ($user) {
            try {
                NotificationService::sendMail(
                    $user->email,
                    new EmailVerificationMail($user)
                );
            } catch (\Exception $e) {
                // Log the error but don't fail the registration
                Log::error('Failed to send verification email: ' . $e->getMessage());
            }
        });

        // Redirect to email verification notice page instead of logging in
        return redirect()->route('verification.notice')->with([
            'status' => 'Registration successful! Please check your email to verify your account.',
            'email' => $user->email
        ]);
    }
}
