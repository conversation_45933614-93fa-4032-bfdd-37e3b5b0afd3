<?php

declare(strict_types=1);

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\WorkingHour;
use App\Services\BranchService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

final class WorkingHourController extends Controller
{
    public function __construct(private readonly BranchService $branchDataService) {}

    public function index(Request $request): Response
    {
        $branchId     = Auth::user()->current_branch_id;
        $workingHour  = WorkingHour::where('branch_id', $branchId)->first();
        $workingHours = $workingHour ? $workingHour->working_hours : [];

        return Inertia::render('Vendor/Branches/Working/List', [
            'workingHours' => $workingHours,
        ]);
    }

    public function create(): Response
    {
        $branchId     = Auth::user()->current_branch_id;
        $workingHour  = WorkingHour::where('branch_id', $branchId)->first();
        $workingHours = $workingHour ? $workingHour->working_hours : $this->branchDataService->getDefaultWorkingHours();

        return Inertia::render('Vendor/Branches/Working/Create', [
            'workingHours' => $workingHours,
        ]);
    }

    public function update(Request $request)
    {
        $branchId  = Auth::user()->current_branch_id;
        $validated = $request->validate([
            'working_hours'             => 'required|array|size:7',
            'working_hours.*.day'       => 'required|string',
            'working_hours.*.open'      => 'required|string',
            'working_hours.*.close'     => 'required|string',
            'working_hours.*.is_closed' => 'required|boolean',
        ]);

        WorkingHour::updateOrCreate(
            ['branch_id' => $branchId],
            ['working_hours' => $validated['working_hours']]
        );

        return redirect()->route('vendor.branches.working-hours.index')
            ->with('success', 'Working hours updated successfully');
    }

    /**
     * API endpoint to get working hours for the current branch
     */
    public function getWorkingHours(Request $request)
    {
        $workingHours = $this->branchDataService->getWorkingDataDetail(Auth::user()->current_branch_id);

        return response()->json(['workingHours' => $workingHours]);
    }

    public function getGuestWorkingHours(Request $request)
    {
        $workingHours = $this->branchDataService->getWorkingDataDetail($request->branch_id);

        return response()->json(['workingHours' => $workingHours]);
    }
}
