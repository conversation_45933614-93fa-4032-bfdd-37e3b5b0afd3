import React from 'react';
import { Link } from '@inertiajs/react';

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationProps {
    links: PaginationLink[];
    className?: string;
}

const Pagination: React.FC<PaginationProps> = ({ links, className = '' }) => {
    const getPageNumber = (label: string): number | null => {
        const match = label.match(/\d+/);
        return match ? parseInt(match[0]) : null;
    };

    const isNumeric = (label: string): boolean => {
        return /^\d+$/.test(label.trim());
    };

    const getDisplayLabel = (label: string): string => {
        if (label.includes('&laquo; Previous')) return 'Previous';
        if (label.includes('Next &raquo;')) return 'Next';
        if (label.includes('&laquo;')) return 'First';
        if (label.includes('&raquo;')) return 'Last';
        return label;
    };

    return (
        <div className={`flex flex-wrap items-center justify-center gap-4 ${className}`}>
            {links.map((link, index) => {
                if (!link.url && !link.active) {
                    return null;
                }

                const isActive = link.active;
                const isNumericPage = isNumeric(link.label);
                const displayLabel = getDisplayLabel(link.label);

                return (
                    <Link
                        key={index}
                        href={link.url || '#'}
                        className={`px-4 py-2 text-sm font-semibold rounded-md transition-colors ${
                            isActive
                                ? 'bg-primary text-white cursor-default'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-[#191e3a] dark:text-white-light dark:hover:bg-primary'
                        } ${!link.url ? 'pointer-events-none opacity-50' : ''}`}
                        onClick={(e) => {
                            if (!link.url || link.active) {
                                e.preventDefault();
                            }
                        }}
                    >
                        {displayLabel}
                    </Link>
                );
            })}
        </div>
    );
};

export default Pagination; 