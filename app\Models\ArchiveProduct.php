<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

final class ArchiveProduct extends Model
{
    protected $fillable = [
        'company_id',
        'original_id',
        'name',
        'sku',
        'price',
        'stock',
        'hsn_id',
        'unit_id',
        'is_active',
        'is_taxable',
        'description',
        'archived_by',
        'archive_reason',
    ];

    protected $casts = [
        'is_active'   => 'boolean',
        'is_taxable'  => 'boolean',
        'price'       => 'decimal:2',
        'archived_at' => 'datetime',
    ];

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(ProductCategory::class, 'archive_product_product_category');
    }

    public function hsn(): BelongsTo
    {
        return $this->belongsTo(ProductHsn::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(ProductUnit::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
