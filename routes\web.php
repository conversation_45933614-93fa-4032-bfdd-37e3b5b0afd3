<?php

use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\NotificationChannelController;
use App\Http\Controllers\Admin\PaymentMethodController;
use App\Http\Controllers\Admin\SubscriptionPlanController;
use App\Http\Controllers\Admin\VendorController;
use App\Http\Controllers\Admin\VendorSubscriptionController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\EmailVerificationController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\SubscriptionCheckoutController;
use App\Http\Controllers\User\UserDashboardController;
use App\Http\Controllers\Vendor\ProfileController;
use App\Http\Controllers\Vendor\VendorFrontController;
use App\Http\Controllers\Vendor\WorkingHourController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', [VendorFrontController::class, 'index'])->name('vendor-home');
Route::get('/get-working-hours', [WorkingHourController::class, 'getWorkingHours'])->name('working-hours-get');
Route::get('/working-hours-get-guest', [WorkingHourController::class, 'getGuestWorkingHours'])->name('working-hours-get-guest');

// Subscription Checkout Routes
Route::get('/checkout/subscription/{plan}', [SubscriptionCheckoutController::class, 'checkout'])->name('subscription.checkout');
Route::post('/checkout/subscription/process', [SubscriptionCheckoutController::class, 'processPayment'])->name('subscription.process-payment');

// Subscription Payment Routes (moved from API to web)
Route::post('/checkout/subscription/create-order', [SubscriptionCheckoutController::class, 'createRazorpayOrder'])->name('subscription.create-order');
Route::post('/checkout/subscription/update-status', [SubscriptionCheckoutController::class, 'updateTransactionStatus'])->name('subscription.update-status');

Route::middleware(['auth', 'role:customer', 'central'])->prefix('user')->name('user.')->group(function () {
    Route::get('/dashboard', [UserDashboardController::class, 'index'])->name('dashboard');
});

Route::middleware(['auth', 'role:admin', 'central'])->prefix('siteadmin')->name('siteadmin.')->group(function () {

    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // Subscription Plan Management
    Route::resource('subscription-plans', SubscriptionPlanController::class)->except(['show']);
    Route::get('subscription-plans/trashed', [SubscriptionPlanController::class, 'trashed'])->name('subscription-plans.trashed');
    Route::put('subscription-plans/{subscription_plan}/restore', [SubscriptionPlanController::class, 'restore'])->name('subscription-plans.restore');

    // Vendor Subscription (Purchased Plans) Management
    Route::resource('vendor-subscriptions', VendorSubscriptionController::class)->only(['index', 'edit', 'update', 'destroy']);
    Route::get('vendor-subscriptions/trashed', [VendorSubscriptionController::class, 'trashed'])->name('vendor-subscriptions.trashed');
    Route::put('vendor-subscriptions/{vendor_subscription}/restore', [VendorSubscriptionController::class, 'restore'])->name('vendor-subscriptions.restore');

    // Vendor Management
    Route::resource('vendors', VendorController::class)->only(['index', 'show', 'edit', 'update', 'destroy']);
    Route::post('vendors/{vendor}/access-portal', [VendorController::class, 'accessPortal'])->name('vendors.access-portal');

    // Payment Method Management
    Route::resource('payment-methods', PaymentMethodController::class)->except(['show']);
    Route::get('payment-methods/trashed', [PaymentMethodController::class, 'trashed'])->name('payment-methods.trashed');
    Route::put('payment-methods/{payment_method}/restore', [PaymentMethodController::class, 'restore'])->name('payment-methods.restore');

    // Notification Channel Management
    Route::resource('notification-channels', NotificationChannelController::class)->except(['show']);
    Route::get('notification-channels/trashed', [NotificationChannelController::class, 'trashed'])->name('notification-channels.trashed');
    Route::put('notification-channels/{notification_channel}/restore', [NotificationChannelController::class, 'restore'])->name('notification-channels.restore');

});

// Main Dashboard - Protected routes
Route::middleware(['auth'])->group(function () {

    // Profile Management Routes
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'show'])->name('show');
        Route::post('/', [ProfileController::class, 'update'])->name('update');
        Route::post('/password', [ProfileController::class, 'updatePassword'])->name('password');
    });

});

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
| Routes for user authentication and account management
*/

// Guest-only routes
Route::middleware(['guest'])->group(function () {
    // Authentication Routes
    Route::get('/auth/login', [AuthenticatedSessionController::class, 'create'])
        ->name('login');
    Route::post('/auth/login', [AuthenticatedSessionController::class, 'store']);

    // Password Reset Routes
    Route::get('/auth/forgot-password', [PasswordResetLinkController::class, 'create'])
        ->name('password.request');
    Route::post('/auth/forgot-password', [PasswordResetLinkController::class, 'store'])
        ->name('password.email');
    Route::get('/auth/reset-password/{token}', [NewPasswordController::class, 'create'])
        ->name('password.reset');
    Route::post('/auth/reset-password', [NewPasswordController::class, 'store'])
        ->name('password.store');

    // Email Verification Routes
    Route::get('/auth/verify-email', [EmailVerificationController::class, 'notice'])
        ->name('verification.notice');
    Route::get('/auth/verify-email/{token}', [EmailVerificationController::class, 'verify'])
        ->name('verification.verify');
    Route::post('/auth/verify-email/resend', [EmailVerificationController::class, 'resend'])
        ->name('verification.resend');

    Route::middleware(['central'])->group(function () {
        Route::get('/auth/register', [RegisteredUserController::class, 'create'])
            ->name('register');
        Route::post('/auth/register', [RegisteredUserController::class, 'store']);
    });

});

// Logout route (for authenticated users)
Route::post('/auth/logout', [AuthenticatedSessionController::class, 'destroy'])->middleware('auth')->name('logout');

// Vendor redirect route
Route::get('/vendor/redirect', function () {
    $redirectData = session('user_redirect_data');

    if (isset($redirectData['admin_user_id']) && $redirectData['admin_user_id'] != '') {
        session(['admin_user_id' => $redirectData['admin_user_id']]);
    }

    return Inertia::render('VendorRedirect', [
        'redirectUrl'  => $redirectData['redirect_url'],
        'redirectData' => $redirectData,
        'layout'       => 'blank',
    ]);
})->name('vendor.redirect');
