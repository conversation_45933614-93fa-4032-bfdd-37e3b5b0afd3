<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class PlanTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'payment_method_id',
        'razorpay_order_id',
        'razorpay_payment_id',
        'razorpay_signature',
        'razorpay_order_data',
        'razorpay_payment_data',
        'amount',
        'currency',
        'status',
        'payment_status',
        'notes',
        'paid_at',
        'failed_at',
        'failure_reason',
    ];

    protected $casts = [
        'razorpay_order_data'   => 'array',
        'razorpay_payment_data' => 'array',
        'amount'                => 'decimal:2',
        'paid_at'               => 'datetime',
        'failed_at'             => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class);
    }

    public function vendorSubscription(): HasOne
    {
        return $this->hasOne(VendorSubscription::class);
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    // Helper methods
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status'         => 'completed',
            'payment_status' => 'captured',
            'paid_at'        => now(),
        ]);
    }

    public function markAsFailed(?string $reason = null): void
    {
        $this->update([
            'status'         => 'failed',
            'payment_status' => 'failed',
            'failed_at'      => now(),
            'failure_reason' => $reason,
        ]);
    }
}
