import React from 'react';
import { Link, router } from '@inertiajs/react';
import Swal from 'sweetalert2';
import { useRoute } from '@hooks/useRoute';

interface NotificationChannel {
    id: number;
    name: string;
    driver: string;
    is_active: boolean;
    deleted_at?: string | null;
}

interface Props {
    channels: NotificationChannel[];
    flash?: { success?: string; error?: string };
}

const Trashed = ({ channels, flash }: Props) => {
    const { route } = useRoute();

    const handleRestore = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Do you want to restore this notification channel?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, restore it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.put(route('siteadmin.notification-channels.restore', { notification_channel: id }), {}, {
                    preserveScroll: true,
                });
            }
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.notification-channels.index')} className="text-primary hover:underline">
                        Notification Channels
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Trashed</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Trashed Notification Channels</h5>
                        <Link href={route('siteadmin.notification-channels.index')} className="btn btn-outline-primary">
                            Back to List
                        </Link>
                    </div>
                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Driver</th>
                                    <th>Status</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {channels.length === 0 ? (
                                    <tr>
                                        <td colSpan={5} className="text-center">No trashed notification channels found.</td>
                                    </tr>
                                ) : (
                                    channels.map((channel) => (
                                        <tr key={channel.id}>
                                            <td>{channel.id}</td>
                                            <td>{channel.name}</td>
                                            <td>{channel.driver}</td>
                                            <td>
                                                <span className={`badge badge-outline-${channel.is_active ? 'success' : 'danger'}`}>
                                                    {channel.is_active ? 'Active' : 'Inactive'}
                                                </span>
                                            </td>
                                            <td className="text-center">
                                                <button type="button" className="btn btn-sm btn-outline-success" onClick={() => handleRestore(channel.id)}>
                                                    Restore
                                                </button>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Trashed;
