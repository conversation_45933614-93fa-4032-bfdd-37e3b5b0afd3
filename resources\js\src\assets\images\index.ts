// Auth Images - using string paths for runtime resolution
const mapPng = '/assets/images/auth/map.png';
const bgGradientPng = '/assets/images/auth/bg-gradient.png';
const comingSoonObject1Png = '/assets/images/auth/coming-soon-object1.png';
const comingSoonObject2Png = '/assets/images/auth/coming-soon-object2.png';
const comingSoonObject3Png = '/assets/images/auth/coming-soon-object3.png';
const polygonObjectSvg = '/assets/images/auth/polygon-object.svg';
const logoWhiteSvg = '/assets/images/auth/logo-white.svg';
const contactUsSvg = '/assets/images/auth/contact-us.svg';
const loginSvg = '/assets/images/auth/login.svg';
const registerSvg = '/assets/images/auth/register.svg';

// General Images - using string paths for runtime resolution
const logoSvg = '/assets/images/logo.svg';
const closeSvg = '/assets/images/close.svg';
const checkedSvg = '/assets/images/checked.svg';
const menuHeadeJpg = '/assets/images/menu-heade.jpg';

// Profile Images - using string paths for runtime resolution
const profile34Jpeg = '/assets/images/profile-34.jpeg';

// Error Images - using string paths for runtime resolution
const error404DarkSvg = '/assets/images/error/404-dark.svg';
const error404LightSvg = '/assets/images/error/404-light.svg';
const error500DarkSvg = '/assets/images/error/500-dark.svg';
const error500LightSvg = '/assets/images/error/500-light.svg';
const error503DarkSvg = '/assets/images/error/503-dark.svg';
const error503LightSvg = '/assets/images/error/503-light.svg';

// Export all assets
export const authImages = {
    map: mapPng,
    bgGradient: bgGradientPng,
    comingSoonObject1: comingSoonObject1Png,
    comingSoonObject2: comingSoonObject2Png,
    comingSoonObject3: comingSoonObject3Png,
    polygonObject: polygonObjectSvg,
    logoWhite: logoWhiteSvg,
    contactUs: contactUsSvg,
    login: loginSvg,
    register: registerSvg,
};

export const generalImages = {
    logo: logoSvg,
    close: closeSvg,
    checked: checkedSvg,
    menuHeade: menuHeadeJpg,
};

export const profileImages = {
    profile34: profile34Jpeg,
};

export const errorImages = {
    error404Dark: error404DarkSvg,
    error404Light: error404LightSvg,
    error500Dark: error500DarkSvg,
    error500Light: error500LightSvg,
    error503Dark: error503DarkSvg,
    error503Light: error503LightSvg,
};

// Default export with all images
export default {
    auth: authImages,
    general: generalImages,
    profile: profileImages,
    error: errorImages,
};
