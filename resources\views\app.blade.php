<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2, shrink-to-fit=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="keyword" content="Salozy, Salon CRM, Salon ERP, Salon Management">
    <meta name="description" content="Salozy a complete solution to manage your salon digitally.">

    <title>Salozy - Product by Astrid Web Technology LLP</title>

    <link rel="shortcut icon" href="{{ url('favicon.png') }}">

    <link href="https://fonts.googleapis.com/css?family=Nunito:400,600,700" rel="stylesheet" defer>

    @routes
    @viteReactRefresh
    @vite(['resources/js/src/main.tsx'])
    @inertiaHead
</head>

<body>
    <noscript>
        <strong>We're sorry but <PERSON><PERSON><PERSON> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>

    @inertia
</body>

</html>



