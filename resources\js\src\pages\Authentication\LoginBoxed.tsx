import { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { useDispatch, useSelector } from 'react-redux';
import i18next from 'i18next';
import Dropdown from '@components/Dropdown';
import { IRootState } from '@store';
import { setPageTitle, toggleRTL } from '@store/themeConfigSlice';
import { authImages } from '@images';

const LoginBoxed = ({ canResetPassword, status, data, errors = {} }) => {
    const dispatch = useDispatch();
    useEffect(() => {
        dispatch(setPageTitle('Login Boxed'));
    });
    const isDark = useSelector((state: IRootState) => state.themeConfig.theme === 'dark' || state.themeConfig.isDarkMode);
    const isRtl = useSelector((state: IRootState) => state.themeConfig.rtlClass) === 'rtl' ? true : false;
    const themeConfig = useSelector((state: IRootState) => state.themeConfig);
    const setLocale = (flag: string) => {
        setFlag(flag);
        if (flag.toLowerCase() === 'ae') {
            dispatch(toggleRTL('rtl'));
        } else {
            dispatch(toggleRTL('ltr'));
        }
    };
    const [flag, setFlag] = useState(themeConfig.locale);

    const [formData, setFormData] = useState({
        email: '',
        password: '',
        remember: false,
    });

    const [processing, setProcessing] = useState(false);

    const handleChange = (e) => {
        const { id, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [id]: type === 'checkbox' ? checked : value
        }));
    };

    const submitForm = (e) => {
        e.preventDefault();
        setProcessing(true);
        router.post('/auth/login', formData, {
            onFinish: () => setProcessing(false)
        });
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
            {/* Subtle animated background elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                <div className="absolute top-20 left-10 w-32 h-32 bg-orange-800/5 rounded-full animate-float-slow"></div>
                <div className="absolute top-40 right-20 w-24 h-24 bg-gray-400/5 rounded-full animate-float-medium"></div>
                <div className="absolute bottom-40 left-20 w-40 h-40 bg-orange-800/5 rounded-full animate-float-slow"></div>
                <div className="absolute bottom-20 right-10 w-28 h-28 bg-gray-400/5 rounded-full animate-float-fast"></div>
            </div>

            <div className="relative flex justify-center items-center px-6 py-10 min-h-screen">
                <div className="w-full max-w-md">
                    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl border border-gray-200/50 dark:border-gray-700/50 rounded-3xl shadow-2xl p-8 transform transition-all duration-300 hover:shadow-3xl">

                        {/* Header Section */}
                        <div className="text-center mb-8">
                            {data['logo'] && (
                                <div className="flex justify-center mb-6">
                                    <div className="relative group">
                                        <div className="absolute inset-0 bg-orange-800/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                                        <img
                                            src={data['logo']}
                                            alt="Logo"
                                            className="relative w-20 h-20 object-contain rounded-2xl transform transition-transform duration-300 group-hover:scale-105"
                                        />
                                    </div>
                                </div>
                            )}

                            <h1 className="text-3xl font-black text-gray-900 dark:text-white mb-2">
                                Welcome Back
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400 font-medium">
                                Sign in to your account to continue
                            </p>
                        </div>


                        {status && (
                            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-2xl p-4 mb-6">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-green-800 dark:text-green-200 font-medium">{status}</p>
                                    </div>
                                </div>
                            </div>
                        )}

                        <form className="space-y-6" onSubmit={submitForm}>
                            <div className="space-y-2">
                                <label htmlFor="email" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                    Email Address
                                </label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                                        </svg>
                                    </div>
                                    <input
                                        id="email"
                                        type="email"
                                        placeholder="Enter your email address"
                                        className="w-full pl-12 pr-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-800/20 focus:border-orange-800 transition-all duration-300"
                                        value={formData.email}
                                        onChange={handleChange}
                                        required
                                    />
                                </div>
                                {errors.email && (
                                    <div className="flex items-center mt-2 text-red-600 dark:text-red-400">
                                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                        </svg>
                                        <span className="text-sm font-medium">{errors.email}</span>
                                    </div>
                                )}
                            </div>
                            <div className="space-y-2">
                                <label htmlFor="password" className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                    Password
                                </label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                        </svg>
                                    </div>
                                    <input
                                        id="password"
                                        type="password"
                                        placeholder="Enter your password"
                                        className="w-full pl-12 pr-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-800/20 focus:border-orange-800 transition-all duration-300"
                                        value={formData.password}
                                        onChange={handleChange}
                                        required
                                    />
                                </div>
                                {errors.password && (
                                    <div className="flex items-center mt-2 text-red-600 dark:text-red-400">
                                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                        </svg>
                                        <span className="text-sm font-medium">{errors.password}</span>
                                    </div>
                                )}
                            </div>
                            <div className="flex justify-between items-center">
                                <label className="flex items-center cursor-pointer group">
                                    <input
                                        type="checkbox"
                                        id="remember"
                                        className="w-4 h-4 text-orange-800 bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-orange-800 focus:ring-2 transition-all duration-200"
                                        checked={formData.remember}
                                        onChange={handleChange}
                                    />
                                    <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-orange-800 transition-colors duration-200">
                                        Remember me
                                    </span>
                                </label>
                                {canResetPassword && (
                                    <Link
                                        href="#"
                                        className="text-sm font-semibold text-orange-800 hover:text-orange-900 dark:text-orange-600 dark:hover:text-orange-500 transition-colors duration-200 hover:underline"
                                    >
                                        Forgot Password?
                                    </Link>
                                )}
                            </div>
                            <button
                                type="submit"
                                className="w-full mt-8 px-6 py-3 bg-gradient-to-r from-orange-800 to-orange-900 hover:from-orange-900 hover:to-orange-800 text-white font-bold rounded-2xl shadow-lg hover:shadow-xl transform transition-all duration-300 hover:scale-[1.02] focus:outline-none focus:ring-4 focus:ring-orange-800/30 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={processing}
                            >
                                <div className="flex items-center justify-center">
                                    {processing ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Signing in...
                                        </>
                                    ) : (
                                        <>
                                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                            </svg>
                                            Sign In
                                        </>
                                    )}
                                </div>
                            </button>
                        </form>

                        {data['central_domain'] && (
                            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 text-center">
                                <p className="text-gray-600 dark:text-gray-400 font-medium">
                                    Don't have an account?{' '}
                                    <Link
                                        href="/auth/register"
                                        className="font-bold text-orange-800 hover:text-orange-900 dark:text-orange-600 dark:hover:text-orange-500 transition-colors duration-200 hover:underline"
                                    >
                                        Sign Up
                                    </Link>
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LoginBoxed;
