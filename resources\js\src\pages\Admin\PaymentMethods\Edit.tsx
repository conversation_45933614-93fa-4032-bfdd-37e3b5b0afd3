import React, { useEffect, useState } from 'react';
import { Link, useForm } from '@inertiajs/react';
import Swal from 'sweetalert2';
import { useRoute } from '@hooks/useRoute';

interface PaymentMethod {
    id: number;
    name: string;
    type: string;
    mode: 'live' | 'test';
    details: any;
    status: 'active' | 'deactive';
}

interface Props {
    method: PaymentMethod;
    flash?: { success?: string; error?: string };
}

const Edit = ({ method, flash }: Props) => {
    const { route } = useRoute();
    const [details, setDetails] = useState<{ key: string; value: string }[]>([]);
    const { data, setData, put, processing, errors } = useForm({
        name: method.name,
        type: method.type,
        mode: method.mode,
        status: method.status,
        details: method.details || {},
    });

    useEffect(() => {
        // Convert details object to array of key-value pairs
        if (method.details && typeof method.details === 'object') {
            setDetails(Object.entries(method.details).map(([key, value]) => ({ key, value: String(value) })));
        } else {
            setDetails([{ key: '', value: '' }]);
        }
    }, [method.details]);

    // Update form data whenever details change
    useEffect(() => {
        const detailsObj: Record<string, string> = {};
        for (const pair of details) {
            if (pair.key) detailsObj[pair.key] = pair.value;
        }
        setData('details', detailsObj);
    }, [details]);

    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleDetailChange = (idx: number, field: 'key' | 'value', value: string) => {
        setDetails((prev) => {
            const updated = [...prev];
            updated[idx][field] = value;
            return updated;
        });
    };

    const addDetail = () => {
        setDetails((prev) => [...prev, { key: '', value: '' }]);
    };

    const removeDetail = (idx: number) => {
        setDetails((prev) => prev.filter((_, i) => i !== idx));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Convert details array to object
        const detailsObj: Record<string, string> = {};
        for (const pair of details) {
            if (pair.key.trim()) detailsObj[pair.key.trim()] = pair.value;
        }
        if (Object.keys(detailsObj).length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Please add at least one key-value pair in details.',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
            return;
        }
        // Submit with updated details
        put(route('siteadmin.payment-methods.update', { payment_method: method.id }), {
            ...data,
            details: detailsObj,
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.payment-methods.index')} className="text-primary hover:underline">
                        Payment Methods
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Edit Payment Method</h5>
                    </div>
                    <form className="space-y-5" onSubmit={handleSubmit} autoComplete="off">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label htmlFor="name">Name <span className="text-danger">*</span></label>
                                <input id="name" type="text" className="form-input" value={data.name} onChange={e => setData('name', e.target.value)} required />
                                {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                            </div>
                            <div>
                                <label htmlFor="type">Type <span className="text-danger">*</span></label>
                                <input id="type" type="text" className="form-input" value={data.type} onChange={e => setData('type', e.target.value)} required />
                                {errors.type && <div className="text-danger mt-1">{errors.type}</div>}
                            </div>
                            <div>
                                <label htmlFor="mode">Mode <span className="text-danger">*</span></label>
                                <select id="mode" className="form-input" value={data.mode} onChange={e => setData('mode', e.target.value as 'live' | 'test')} required>
                                    <option value="test">Test</option>
                                    <option value="live">Live</option>
                                </select>
                                {errors.mode && <div className="text-danger mt-1">{errors.mode}</div>}
                            </div>
                            <div>
                                <label htmlFor="status">Status <span className="text-danger">*</span></label>
                                <select id="status" className="form-input" value={data.status} onChange={e => setData('status', e.target.value as 'active' | 'deactive')} required>
                                    <option value="active">Active</option>
                                    <option value="deactive">Deactive</option>
                                </select>
                                {errors.status && <div className="text-danger mt-1">{errors.status}</div>}
                            </div>
                        </div>
                        <div>
                            <label className="block font-semibold mb-2">Details (Key-Value Pairs) <span className="text-danger">*</span></label>
                            <div className="space-y-2">
                                {details.map((pair, idx) => (
                                    <div key={idx} className="flex gap-2 items-center">
                                        <input
                                            type="text"
                                            className="form-input flex-1"
                                            placeholder="Key"
                                            value={pair.key}
                                            onChange={e => handleDetailChange(idx, 'key', e.target.value)}
                                            required
                                        />
                                        <input
                                            type="text"
                                            className="form-input flex-1"
                                            placeholder="Value"
                                            value={pair.value}
                                            onChange={e => handleDetailChange(idx, 'value', e.target.value)}
                                            required
                                        />
                                        {details.length > 1 && (
                                            <button type="button" className="btn btn-sm btn-outline-danger" onClick={() => removeDetail(idx)}>
                                                Remove
                                            </button>
                                        )}
                                    </div>
                                ))}
                                <button type="button" className="btn btn-sm btn-outline-primary mt-2" onClick={addDetail}>
                                    + Add Key-Value
                                </button>
                            </div>
                        </div>
                        <div className="flex items-center justify-end gap-4 mt-4">
                            <Link href={route('siteadmin.payment-methods.index')} className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary" disabled={processing}>
                                {processing ? 'Saving...' : 'Save Changes'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Edit;
