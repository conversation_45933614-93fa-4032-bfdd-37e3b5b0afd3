<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendor_subscriptions', function (Blueprint $table) {
            $table->foreignId('plan_transaction_id')->nullable()->after('subscription_plan_id')->constrained()->onDelete('set null');
            $table->index('plan_transaction_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendor_subscriptions', function (Blueprint $table) {
            $table->dropForeign(['plan_transaction_id']);
            $table->dropIndex(['plan_transaction_id']);
            $table->dropColumn('plan_transaction_id');
        });
    }
};
