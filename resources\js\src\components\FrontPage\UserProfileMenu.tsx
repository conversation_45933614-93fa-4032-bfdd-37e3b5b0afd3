import React, { useRef, useEffect } from 'react';
import { router } from '@inertiajs/react';

interface AuthUser {
    id: number;
    role: string[];
    name: string;
    email: string;
    profile?: string | null;
}

interface UserProfileMenuProps {
    authUser: AuthUser;
    userMenuOpen: boolean;
    setUserMenuOpen: (open: boolean) => void;
}

const UserProfileMenu: React.FC<UserProfileMenuProps> = ({
    authUser,
    userMenuOpen,
    setUserMenuOpen
}) => {
    const userMenuRef = useRef<HTMLDivElement>(null);

    // Helper for profile image URL
    const getProfileImageUrl = (profilePath: string | null | undefined) => {
        if (!profilePath) return '/assets/images/profile-34.jpeg';
        const tenantDomain = window.location.hostname;
        return `https://${tenantDomain}/${profilePath}`;
    };

    // Determine dashboard route
    const isVendor = authUser && Array.isArray(authUser.role) && authUser.role.some(r => r.toLowerCase().includes('vendor'));
    const dashboardRoute = isVendor ? '/vendor/dashboard' : '/user/dashboard';

    // Close menu on outside click
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setUserMenuOpen(false);
            }
        }
        if (userMenuOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [userMenuOpen, setUserMenuOpen]);

    return (
        <div className="top-5 right-4 z-50 fixed" ref={userMenuRef}>
            <button
                className="focus:outline-none"
                onClick={() => setUserMenuOpen(!userMenuOpen)}
                aria-label="User menu"
            >
                <img
                    src={getProfileImageUrl(authUser.profile)}
                    alt="Profile"
                    className="bg-white shadow-lg border-2 border-orange-800 rounded-full w-12 h-12 object-cover"
                />
            </button>
            {userMenuOpen && (
                <div className="right-0 z-50 absolute bg-white shadow-xl mt-2 py-2 border border-gray-100 rounded-xl w-48 animate-fade-in">
                    <div className="px-4 py-2 border-gray-100 border-b">
                        <div className="font-semibold text-gray-800 truncate">{authUser.name}</div>
                        <div className="text-gray-600 text-xs truncate">{authUser.email}</div>
                    </div>
                    <a
                        href={dashboardRoute}
                        className="block hover:bg-orange-50 px-4 py-2 rounded-t-xl text-gray-700 hover:text-orange-800 transition"
                    >
                        Dashboard
                    </a>
                    <button
                        onClick={(e) => {
                            e.preventDefault();
                            router.post('/auth/logout');
                        }}
                        className="hover:bg-red-50 px-4 py-2 rounded-b-xl w-full text-gray-700 text-left hover:text-red-600 transition"
                    >
                        Logout
                    </button>
                </div>
            )}
        </div>
    );
};

export default UserProfileMenu;
