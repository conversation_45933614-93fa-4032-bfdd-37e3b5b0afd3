import React from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fa<PERSON><PERSON><PERSON>, FaGift, FaArrowRight } from 'react-icons/fa';

interface Plan {
    id: number;
    name: string;
    description: string;
    price: number;
    validity_days: number;
    status: string;
    services: Array<{
        id: number;
        name: string;
        allowed_count: number;
    }>;
}

interface MembershipPlansProps {
    plans: Plan[];
    currency_symbol: string;
    handlePlanSelect: (plan: Plan) => void;
}

const MembershipPlans: React.FC<MembershipPlansProps> = ({
    plans,
    currency_symbol,
    handlePlanSelect
}) => {
    if (plans.length === 0) {
        return null;
    }

    return (
        <div className="relative py-20 sm:py-20 bg-gray-50">
            <div className="mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
                {/* Section Header */}
                <div className="mb-16 text-center">
                    <div className="inline-flex justify-center items-center bg-orange-800 mb-6 rounded-2xl w-16 h-16">
                        <FaCrown className="w-8 h-8 text-white" />
                    </div>
                    <h2 className="mb-6 font-black text-4xl sm:text-5xl lg:text-6xl text-gray-900">
                        Membership Plans
                    </h2>
                    <p className="mx-auto max-w-3xl text-gray-700 text-xl leading-relaxed">
                        Unlock exclusive benefits and save more with our premium membership plans
                    </p>
                </div>

                {/* Plans Grid */}
                <div className="gap-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                    {plans.map((plan, index) => (
                        <div
                            key={plan.id}
                            className={`relative group ${index === 1 ? 'lg:scale-110 lg:z-10' : ''}`}
                        >
                            {/* Popular Badge */}
                            {index === 1 && (
                                <div className="-top-4 left-1/2 z-20 absolute transform -translate-x-1/2">
                                    <div className="bg-orange-800 shadow-lg px-6 py-2 rounded-full font-bold text-sm text-white">
                                        Most Popular
                                    </div>
                                </div>
                            )}

                            <div className="relative bg-white hover:shadow-3xl border border-gray-200 rounded-3xl transition-all group-hover:-translate-y-2 duration-500 overflow-hidden">
                                {/* Plan Header */}
                                <div className={`p-8 ${index === 1 ? 'bg-orange-50' : 'bg-gray-50'}`}>
                                    <div className="flex justify-between items-center mb-4">
                                        <h3 className="font-bold text-2xl text-gray-900">{plan.name}</h3>
                                        <div className="bg-white px-3 py-1 rounded-full border border-gray-200">
                                            <span className="font-bold text-orange-800 text-sm">{plan.validity_days} Days</span>
                                        </div>
                                    </div>

                                    <div className="flex items-baseline gap-2 mb-4">
                                        <span className="font-black text-4xl text-orange-800">{currency_symbol}{plan.price}</span>
                                        <span className="text-gray-600 text-lg">/plan</span>
                                    </div>

                                    <p className="text-gray-700 leading-relaxed">{plan.description}</p>
                                </div>

                                {/* Services List */}
                                <div className="p-8">
                                    <div className="space-y-4 mb-8">
                                        <h4 className="flex items-center font-bold text-gray-900 text-lg">
                                            <FaGem className="mr-3 text-orange-800" />
                                            Included Services:
                                        </h4>
                                        {plan.services.map((service) => (
                                            <div key={service.id} className="flex justify-between items-center bg-orange-50 p-4 border border-gray-200 rounded-2xl">
                                                <div className="flex items-center gap-3">
                                                    <FaCheck className="text-green-700 text-sm" />
                                                    <span className="font-medium text-gray-800">{service.name}</span>
                                                </div>
                                                <div className="bg-orange-100 px-3 py-1 rounded-full border border-orange-200">
                                                    <span className="font-bold text-orange-800 text-sm">{service.allowed_count}x</span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>

                                    {/* CTA Button */}
                                    <button
                                        onClick={() => handlePlanSelect(plan)}
                                        className={`w-full group relative overflow-hidden font-bold py-4 px-6 rounded-2xl transition-all duration-500 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-orange-800/30 ${index === 1
                                            ? 'bg-orange-800 text-white hover:bg-orange-900 hover:shadow-2xl'
                                            : 'bg-gray-800 text-white hover:bg-orange-800'
                                            }`}
                                    >
                                        <div className="relative flex justify-center items-center gap-3">
                                            <FaGift className="w-5 h-5" />
                                            <span>Choose This Plan</span>
                                            <FaArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1 duration-300" />
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default MembershipPlans;
