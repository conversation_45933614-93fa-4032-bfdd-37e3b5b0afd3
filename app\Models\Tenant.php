<?php

declare(strict_types=1);

namespace App\Models;

use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\DatabaseConfig;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDomains;

    protected $fillable = [
        'id',
        'data',
    ];

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function database(): DatabaseConfig
    {
        return new DatabaseConfig(
            $this,
            config('database.connections.mysql.database'), // Use your main database name
            config('database.connections.mysql.username'),
            config('database.connections.mysql.password'),
            config('database.connections.mysql.host'),
            config('database.connections.mysql.port')
        );
    }
}
