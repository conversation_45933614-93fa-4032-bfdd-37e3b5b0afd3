<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\SubscriptionPlanService;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class SubscriptionPlanController extends Controller
{
    private SubscriptionPlanService $service;

    public function __construct(SubscriptionPlanService $service)
    {
        $this->service = $service;
    }

    public function index()
    {
        $plans        = $this->service->list();
        $trashedCount = $this->service->trashed()->count();

        return Inertia::render('Admin/SubscriptionPlans/Index', [
            'plans'        => $plans,
            'trashedCount' => $trashedCount,
        ]);
    }

    public function create()
    {
        return Inertia::render('Admin/SubscriptionPlans/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name'            => 'required|string|max:255',
            'description'     => 'nullable|string',
            'price'           => 'required|numeric|min:0',
            'currency_symbol' => 'required|string|max:8',
            'currency_code'   => 'required|string|max:8',
            'billing_cycle'   => 'required|in:monthly,yearly',
            'max_services'    => 'required|integer|min:0',
            'max_branches'    => 'required|integer|min:1',
            'max_staff'       => 'required|integer|min:0',
            'is_active'       => 'required|boolean',
            'sort_order'      => 'nullable|integer',
        ]);
        $this->service->create($validated);

        return redirect()->route('siteadmin.subscription-plans.index')->with('success', 'Subscription plan created successfully.');
    }

    public function edit($id)
    {
        $plan = $this->service->find($id);

        return Inertia::render('Admin/SubscriptionPlans/Edit', [
            'plan' => $plan,
        ]);
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name'            => 'required|string|max:255',
            'description'     => 'nullable|string',
            'price'           => 'required|numeric|min:0',
            'currency_symbol' => 'required|string|max:8',
            'currency_code'   => 'required|string|max:8',
            'billing_cycle'   => 'required|in:monthly,yearly',
            'max_services'    => 'required|integer|min:0',
            'max_branches'    => 'required|integer|min:1',
            'max_staff'       => 'required|integer|min:0',
            'is_active'       => 'required|boolean',
            'sort_order'      => 'nullable|integer',
        ]);
        $this->service->update($id, $validated);

        return redirect()->route('siteadmin.subscription-plans.index')->with('success', 'Subscription plan updated successfully.');
    }

    public function destroy($id)
    {
        try {
            $this->service->delete($id);

            return redirect()->route('siteadmin.subscription-plans.index')->with('success', 'Subscription plan deleted successfully.');
        } catch (ValidationException $e) {
            return redirect()->route('siteadmin.subscription-plans.index')->with('error', $e->getMessage());
        }
    }

    public function trashed()
    {
        $plans = $this->service->trashed();

        return Inertia::render('Admin/SubscriptionPlans/Trashed', [
            'plans' => $plans,
        ]);
    }

    public function restore($id)
    {
        $this->service->restore($id);

        return redirect()->route('siteadmin.subscription-plans.trashed')->with('success', 'Subscription plan restored successfully.');
    }
}
