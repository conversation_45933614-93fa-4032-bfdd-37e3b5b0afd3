<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SubscriptionPlan;
use App\Services\Admin\VendorSubscriptionService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class VendorSubscriptionController extends Controller
{
    private VendorSubscriptionService $service;

    public function __construct(VendorSubscriptionService $service)
    {
        $this->service = $service;
    }

    public function index()
    {
        $subscriptions = $this->service->list();
        $trashedCount  = $this->service->trashed()->count();

        return Inertia::render('Admin/VendorSubscriptions/Index', [
            'subscriptions' => $subscriptions,
            'trashedCount'  => $trashedCount,
        ]);
    }

    public function edit($id)
    {
        $subscription = $this->service->find($id);
        $plans        = SubscriptionPlan::select('id', 'name')->orderBy('name')->get();

        return Inertia::render('Admin/VendorSubscriptions/Edit', [
            'subscription' => $subscription,
            'plans'        => $plans,
        ]);
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'status'               => 'required|in:active,inactive,cancelled,expired,trial',
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'ends_at'              => 'required|date',
            'trial_ends_at'        => 'nullable|date',
        ]);
        $this->service->update($id, $validated);

        return redirect()->route('siteadmin.vendor-subscriptions.index')->with('success', 'Subscription status updated successfully.');
    }

    public function destroy($id)
    {
        $this->service->delete($id);

        return redirect()->route('siteadmin.vendor-subscriptions.index')->with('success', 'Subscription deleted successfully.');
    }

    public function trashed()
    {
        $subscriptions = $this->service->trashed();

        return Inertia::render('Admin/VendorSubscriptions/Trashed', [
            'subscriptions' => $subscriptions,
        ]);
    }

    public function restore($id)
    {
        $this->service->restore($id);

        return redirect()->route('siteadmin.vendor-subscriptions.trashed')->with('success', 'Subscription restored successfully.');
    }
}
