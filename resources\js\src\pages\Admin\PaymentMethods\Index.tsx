import React, { useEffect } from 'react';
import { Link, router } from '@inertiajs/react';
import Swal from 'sweetalert2';
import { useRoute } from '@hooks/useRoute';

interface PaymentMethod {
    id: number;
    name: string;
    type: string;
    mode: 'live' | 'test';
    status: 'active' | 'deactive';
    deleted_at?: string | null;
}

interface Props {
    methods: PaymentMethod[];
    trashedCount: number;
    flash?: { success?: string; error?: string };
}

const Index = ({ methods, trashedCount, flash }: Props) => {
    const { route } = useRoute();
    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleDelete = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to delete this payment method?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(route('siteadmin.payment-methods.destroy', { payment_method: id }), {
                    preserveScroll: true,
                });
            }
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Payment Methods</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Payment Methods</h5>
                        <div className="flex gap-2">
                            <Link
                                href={route('siteadmin.payment-methods.trashed')}
                                className={`btn btn-outline-secondary relative ${trashedCount === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
                                disabled={trashedCount === 0}
                            >
                                View Trashed
                                {trashedCount > 0 && (
                                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                        {trashedCount}
                                    </span>
                                )}
                            </Link>
                            <Link href={route('siteadmin.payment-methods.create')} className="btn btn-primary">
                                Add Payment Method
                            </Link>
                        </div>
                    </div>
                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Mode</th>
                                    <th>Status</th>
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {methods.map((method) => (
                                    <tr key={method.id} className={method.deleted_at ? 'opacity-50' : ''}>
                                        <td>{method.id}</td>
                                        <td>{method.name}</td>
                                        <td>{method.type}</td>
                                        <td>{method.mode}</td>
                                        <td>
                                            <span className={`badge badge-outline-${method.status === 'active' ? 'success' : 'danger'}`}>
                                                {method.status === 'active' ? 'Active' : 'Deactive'}
                                            </span>
                                        </td>
                                        <td className="text-center">
                                            <div className="flex gap-2 items-center justify-center">
                                                <Link href={route('siteadmin.payment-methods.edit', { payment_method: method.id })} className="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </Link>
                                                <button type="button" className="btn btn-sm btn-outline-danger" onClick={() => handleDelete(method.id)} disabled={!!method.deleted_at}>
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Index;
