import React, { useEffect } from 'react';
import { useForm, usePage } from '@inertiajs/react';
import Swal from 'sweetalert2';

interface AddCustomerModalProps {
    open: boolean;
    onClose: () => void;
    onSuccess?: (customer: any) => void;
}

const AddCustomerModal: React.FC<AddCustomerModalProps> = ({ open, onClose, onSuccess }) => {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: '',
        email: '',
        phone: '',
        gender: '',
        address: '',
    });

    useEffect(() => {
        if (open) {
            reset();
            setData({ name: '', email: '', phone: '', gender: '', address: '' });
        }
    }, [open, reset, setData]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setData(name as keyof typeof data, value);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/vendor/manage-customer', {
            preserveScroll: true,
            onSuccess: (page) => {
                Swal.fire({
                    icon: 'success',
                    title: 'Customer added!',
                    text: 'Customer has been added successfully.',
                    timer: 2000,
                    showConfirmButton: false,
                    customClass: { popup: 'sweet-alerts' },
                });
                if (onSuccess) {
                    onSuccess(page?.props?.customer || null);
                }
                reset();
                setData({ name: '', email: '', phone: '', gender: '', address: '' });
            },
            onError: (errors) => {
                if (typeof errors === 'string') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: errors,
                        timer: 2000,
                        showConfirmButton: false,
                        customClass: { popup: 'sweet-alerts' },
                    });
                }
            },
        });
    };

    if (!open) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
                <button
                    className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                    onClick={onClose}
                    type="button"
                >
                    &times;
                </button>
                <h2 className="text-xl font-semibold mb-4">Add Customer</h2>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label htmlFor="name" className="block font-medium">Name</label>
                        <input
                            name="name"
                            className="form-input w-full"
                            value={data.name}
                            onChange={handleChange}
                        />
                        {errors.name && <div className="text-danger mt-1">{errors.name}</div>}
                    </div>
                    <div>
                        <label htmlFor="email" className="block font-medium">Email</label>
                        <input
                            name="email"
                            type="email"
                            className="form-input w-full"
                            value={data.email}
                            onChange={handleChange}
                        />
                        {errors.email && <div className="text-danger mt-1">{errors.email}</div>}
                    </div>
                    <div>
                        <label htmlFor="phone" className="block font-medium">Phone</label>
                        <input
                            name="phone"
                            className="form-input w-full"
                            value={data.phone}
                            onChange={handleChange}
                        />
                        {errors.phone && <div className="text-danger mt-1">{errors.phone}</div>}
                    </div>
                    <div>
                        <label htmlFor="gender" className="block font-medium">Gender</label>
                        <select
                            name="gender"
                            className="form-select w-full"
                            value={data.gender}
                            onChange={handleChange}
                        >
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                        {errors.gender && <div className="text-danger mt-1">{errors.gender}</div>}
                    </div>
                    <div>
                        <label htmlFor="address" className="block font-medium">Address</label>
                        <input
                            name="address"
                            className="form-input w-full"
                            value={data.address}
                            onChange={handleChange}
                        />
                        {errors.address && <div className="text-danger mt-1">{errors.address}</div>}
                    </div>
                    <div className="flex justify-end gap-2 mt-4">
                        <button
                            type="button"
                            className="btn btn-outline-secondary"
                            onClick={onClose}
                            disabled={processing}
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="btn btn-primary"
                            disabled={processing}
                        >
                            {processing ? 'Saving...' : 'Add Customer'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AddCustomerModal; 