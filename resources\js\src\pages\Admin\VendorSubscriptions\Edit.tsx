import React, { useEffect } from 'react';
import { Link, useForm, usePage } from '@inertiajs/react';
import Swal from 'sweetalert2';

interface PlanOption {
    id: number;
    name: string;
}

interface Subscription {
    id: number;
    user: { id: number; name: string; email: string };
    subscription_plan: { id: number; name: string };
    status: string;
    subscription_plan_id: number;
    ends_at: string;
    trial_ends_at: string | null;
}

interface Props {
    subscription: Subscription;
    plans: PlanOption[];
    flash?: { success?: string; error?: string };
}

const Edit = ({ subscription, plans, flash }: Props) => {
    const { data, setData, put, processing, errors } = useForm({
        status: subscription.status,
        subscription_plan_id: subscription.subscription_plan_id || subscription.subscription_plan?.id,
        ends_at: subscription.ends_at ? subscription.ends_at.substring(0, 10) : '',
        trial_ends_at: subscription.trial_ends_at ? subscription.trial_ends_at.substring(0, 10) : '',
    });

    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('siteadmin.vendor-subscriptions.update', { vendor_subscription: subscription.id }));
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.vendor-subscriptions.index')} className="text-primary hover:underline">
                        Purchased Plans
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="mb-5">
                        <h5 className="font-semibold text-lg dark:text-white-light">Edit Purchased Plan</h5>
                    </div>
                    <form className="space-y-5" onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label>Vendor</label>
                                <div className="form-input bg-gray-100">{subscription.user?.name} ({subscription.user?.email})</div>
                            </div>
                            <div>
                                <label htmlFor="subscription_plan_id">Plan <span className="text-danger">*</span></label>
                                <select id="subscription_plan_id" className="form-input" value={data.subscription_plan_id} onChange={e => setData('subscription_plan_id', Number(e.target.value))} required>
                                    <option value="">Select Plan</option>
                                    {plans.map(plan => (
                                        <option key={plan.id} value={plan.id}>{plan.name}</option>
                                    ))}
                                </select>
                                {errors.subscription_plan_id && <div className="text-danger mt-1">{errors.subscription_plan_id}</div>}
                            </div>
                            <div>
                                <label htmlFor="status">Status <span className="text-danger">*</span></label>
                                <select id="status" className="form-input" value={data.status} onChange={e => setData('status', e.target.value)} required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="cancelled">Cancelled</option>
                                    <option value="expired">Expired</option>
                                    <option value="trial">Trial</option>
                                </select>
                                {errors.status && <div className="text-danger mt-1">{errors.status}</div>}
                            </div>
                            <div>
                                <label htmlFor="ends_at">Expiry Date <span className="text-danger">*</span></label>
                                <input id="ends_at" type="date" className="form-input" value={data.ends_at} onChange={e => setData('ends_at', e.target.value)} required />
                                {errors.ends_at && <div className="text-danger mt-1">{errors.ends_at}</div>}
                            </div>
                            <div>
                                <label htmlFor="trial_ends_at">Trial Ends At</label>
                                <input id="trial_ends_at" type="date" className="form-input" value={data.trial_ends_at || ''} onChange={e => setData('trial_ends_at', e.target.value)} />
                                {errors.trial_ends_at && <div className="text-danger mt-1">{errors.trial_ends_at}</div>}
                            </div>
                        </div>
                        <div className="flex items-center justify-end gap-4 mt-4">
                            <Link href={route('siteadmin.vendor-subscriptions.index')} className="btn btn-outline-danger">
                                Cancel
                            </Link>
                            <button type="submit" className="btn btn-primary" disabled={processing}>
                                {processing ? 'Saving...' : 'Save Changes'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Edit; 