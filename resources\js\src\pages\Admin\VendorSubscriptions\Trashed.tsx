import React, { useEffect } from 'react';
import { Link, router } from '@inertiajs/react';
import Swal from 'sweetalert2';
import { useRoute } from '@hooks/useRoute';

interface Subscription {
    id: number;
    user: { id: number; name: string; email: string };
    subscription_plan: { id: number; name: string; price: number; currency_symbol: string; currency_code: string };
    status: string;
    starts_at: string;
    ends_at: string;
    deleted_at: string;
}

interface Props {
    subscriptions: Subscription[];
    flash?: { success?: string; error?: string };
}

const Trashed = ({ subscriptions, flash }: Props) => {
    const { route } = useRoute();

    useEffect(() => {
        if (flash?.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash?.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash?.error, flash?.success]);

    const handleRestore = (id: number, vendorName: string) => {
        Swal.fire({
            title: 'Are you sure?',
            html: `Are you sure you want to restore the subscription for "${vendorName}"?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, restore it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.put(route('siteadmin.vendor-subscriptions.restore', { vendor_subscription: id }), {}, {
                    preserveScroll: true,
                });
            }
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusBadgeClass = (status: string) => {
        switch (status) {
            case 'active':
                return 'badge-outline-success';
            case 'expired':
                return 'badge-outline-danger';
            case 'cancelled':
                return 'badge-outline-warning';
            case 'trial':
                return 'badge-outline-info';
            default:
                return 'badge-outline-secondary';
        }
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href={route('siteadmin.dashboard')} className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href={route('siteadmin.vendor-subscriptions.index')} className="text-primary hover:underline">
                        Purchased Plans
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Trashed</span>
                </li>
            </ul>
            <div className="pt-5">
                <div className="panel">
                    <div className="flex items-center justify-between mb-5">
                        <div>
                            <h5 className="font-semibold text-lg dark:text-white-light">Trashed Vendor Subscriptions</h5>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                {subscriptions.length} trashed {subscriptions.length === 1 ? 'subscription' : 'subscriptions'}
                            </p>
                        </div>
                        <Link href={route('siteadmin.vendor-subscriptions.index')} className="btn btn-outline-primary">
                            Back to Active Subscriptions
                        </Link>
                    </div>

                    {subscriptions.length === 0 ? (
                        <div className="text-center py-8">
                            <div className="text-gray-500 dark:text-gray-400">
                                <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                <p className="text-lg font-medium">No trashed subscriptions found</p>
                                <p className="text-sm">All vendor subscriptions are active or have been permanently deleted.</p>
                            </div>
                        </div>
                    ) : (
                        <div className="table-responsive">
                            <table className="table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Vendor</th>
                                        <th>Email</th>
                                        <th>Plan</th>
                                        <th>Price</th>
                                        <th>Currency</th>
                                        <th>Status</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Deleted At</th>
                                        <th className="!text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {subscriptions.map((sub) => (
                                        <tr key={sub.id} className="opacity-75">
                                            <td>{sub.id}</td>
                                            <td>{sub.user.name}</td>
                                            <td>{sub.user.email}</td>
                                            <td>{sub.subscription_plan.name}</td>
                                            <td>{sub.subscription_plan.currency_symbol}{Number(sub.subscription_plan.price).toFixed(2)}</td>
                                            <td>{sub.subscription_plan.currency_code}</td>
                                            <td>
                                                <span className={`badge ${getStatusBadgeClass(sub.status)}`}>
                                                    {sub.status.charAt(0).toUpperCase() + sub.status.slice(1)}
                                                </span>
                                            </td>
                                            <td>{formatDate(sub.starts_at)}</td>
                                            <td>{formatDate(sub.ends_at)}</td>
                                            <td>{formatDate(sub.deleted_at)}</td>
                                            <td className="text-center">
                                                <button
                                                    type="button"
                                                    className="btn btn-sm btn-outline-success"
                                                    onClick={() => handleRestore(sub.id, sub.user.name)}
                                                >
                                                    Restore
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Trashed;
