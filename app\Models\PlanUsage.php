<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlanUsage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'plan_id',
        'branch_id',
        'currency_symbol',
        'currency_text',
        'purchased_at',
        'expires_at',
        'status',
    ];

    protected $casts = [
        'purchased_at' => 'datetime',
        'expires_at'   => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function serviceUsages()
    {
        return $this->hasMany(PlanServiceUsage::class);
    }

    public function planServiceUsages()
    {
        return $this->hasMany(\App\Models\PlanServiceUsage::class, 'plan_usage_id');
    }

    public function isActive()
    {
        return $this->status === 'active' && $this->expires_at->isFuture();
    }

    public function hasAvailableService($serviceId)
    {
        return $this->serviceUsages()
            ->where('service_id', $serviceId)
            ->where('remaining_count', '>', 0)
            ->exists();
    }

    public function getRemainingCount($serviceId)
    {
        $usage = $this->serviceUsages()
            ->where('service_id', $serviceId)
            ->first();

        return $usage ? $usage->remaining_count : 0;
    }
}
