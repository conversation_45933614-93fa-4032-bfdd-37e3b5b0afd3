import { usePage } from '@inertiajs/react';

// This hook provides a compatible API for both React Router and Inertia
export const useRouting = () => {
  const { url } = usePage().props.ziggy;

  return {
    // Mimic React Router's useLocation
    location: {
      pathname: url,
      search: '', // Add if needed from window.location.search
      hash: window.location.hash,
    },
    // Add other routing-related utilities as needed
    isActive: (path: string, exact = false) => {
      return exact ? url === path : url.startsWith(path);
    }
  };
};