import React, { useEffect } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import { setPageTitle } from '@/store/themeConfigSlice';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import Swal from 'sweetalert2';

interface Props {
    service: {
        id: number;
        name: string;
        description: string;
        duration_minutes: number;
        price: number;
        is_active: boolean;
        reminder_after_service: number;
        gender: string;
        total_repeat_service: number;
    };
}

const EditService = ({ service }: Props) => {
    const dispatch = useDispatch();
    const { props } = usePage();
    const auth = props.auth as any || {};
    const branch = auth.user?.branch || {};
    const currency_symbol = branch.currency_symbol || '₹';
    const currency_text = branch.currency_text || 'INR';

    useEffect(() => {
        dispatch(setPageTitle('Edit Service'));
    });

    const validationSchema = Yup.object().shape({
        name: Yup.string().required('Name is required').max(255, 'Name cannot exceed 255 characters'),
        duration_minutes: Yup.number()
            .required('Duration is required')
            .min(5, 'Duration must be at least 5 minutes')
            .max(480, 'Duration cannot exceed 480 minutes'),
        price: Yup.number()
            .required('Price is required')
            .min(1, 'Price must be greater than 0'),
        is_active: Yup.boolean(),
        gender: Yup.string().required('Gender is required').oneOf(['male', 'female'], 'Invalid gender'),
        reminder_after_service: Yup.number().required('Reminder after service is required').min(0, 'Reminder must be at least 0'),
        total_repeat_service: Yup.number()
            .test('required-if-reminder', 'Total repeat service is required when reminder is set',
                function(value) {
                    return !this.parent.reminder_after_service || 
                        (this.parent.reminder_after_service > 0 && value !== undefined && value >= 1);
                })
            .min(1, 'Total repeat service must be at least 1'),
    });

    const initialValues = {
        name: service.name,
        description: service.description,
        duration_minutes: service.duration_minutes,
        price: service.price,
        is_active: service.is_active,
        reminder_after_service: service.reminder_after_service,
        gender: service.gender,
        total_repeat_service: service.total_repeat_service,
    };

    const handleSubmit = (values: any) => {
        router.put(`/vendor/services/${service.id}`, values, {
            onSuccess: () => {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Service has been updated successfully.',
                    padding: '2em',
                    customClass: {
                        container: 'sweet-alerts'
                    }
                });
            },
            onError: (errors) => {
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Something went wrong while updating the service.',
                    padding: '2em',
                    customClass: {
                        container: 'sweet-alerts'
                    }
                });
            },
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <Link href="/vendor/services" className="text-primary hover:underline">
                        Services
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Edit Service</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <Formik
                        initialValues={initialValues}
                        validationSchema={validationSchema}
                        onSubmit={handleSubmit}
                    >
                        {({ errors, touched, values, setFieldValue }) => (
                            <Form className="space-y-5">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                                    <div>
                                        <label htmlFor="name">Service Name <span className="text-danger">*</span></label>
                                        <Field
                                            id="name"
                                            name="name"
                                            type="text"
                                            className="form-input"
                                            placeholder="Enter service name"
                                        />
                                        {errors.name && touched.name && (
                                            <div className="text-danger mt-1">{errors.name}</div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="price">Price ({currency_symbol} {currency_text}) <span className="text-danger">*</span></label>
                                        <Field
                                            id="price"
                                            name="price"
                                            type="number"
                                            className="form-input"
                                            placeholder="Enter price"
                                        />
                                        {errors.price && touched.price && (
                                            <div className="text-danger mt-1">{errors.price}</div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="reminder_after_service">Reminder After Service <span className="text-danger">*</span></label>
                                        <Field
                                            id="reminder_after_service"
                                            name="reminder_after_service"
                                            type="number"
                                            min={0}
                                            className="form-input"
                                            placeholder="Enter Reminder Count"
                                        />
                                        {errors.reminder_after_service && touched.reminder_after_service && (
                                            <div className="text-danger mt-1">{errors.reminder_after_service}</div>
                                        )}
                                    </div>

                                    {values.reminder_after_service > 0 && (
                                        <div>
                                            <label htmlFor='total_repeat_service'>Total Repeat Service</label>
                                            <Field
                                                id="total_repeat_service"
                                                name="total_repeat_service"
                                                type="number"
                                                min={0}
                                                className="form-input"
                                                placeholder="Enter total repeat count"
                                            />
                                            {errors.total_repeat_service && touched.total_repeat_service && (
                                                <div className="text-danger mt-1">{errors.total_repeat_service}</div>
                                            )}
                                        </div>
                                    )}

                                    <div>
                                        <label htmlFor="gender">Gender <span className="text-danger">*</span></label>
                                        <Field as="select" id="gender" name="gender" className="form-select">
                                            <option value="">Select Gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                        </Field>
                                        {errors.gender && touched.gender && (
                                            <div className="text-danger mt-1">{errors.gender}</div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="duration_minutes">Duration (minutes) <span className="text-danger">*</span></label>
                                        <Field
                                            id="duration_minutes"
                                            name="duration_minutes"
                                            type="number"
                                            className="form-input"
                                            placeholder="Enter duration in minutes"
                                        />
                                        {errors.duration_minutes && touched.duration_minutes && (
                                            <div className="text-danger mt-1">{errors.duration_minutes}</div>
                                        )}
                                    </div>

                                    <div>
                                        <label htmlFor="is_active">Status</label>
                                        <div className="mt-2">
                                            <label className="inline-flex">
                                                <Field
                                                    type="checkbox"
                                                    name="is_active"
                                                    className="form-checkbox"
                                                />
                                                <span className="ltr:ml-2 rtl:mr-2">Active</span>
                                            </label>
                                        </div>
                                    </div>

                                    <div className="md:col-span-2">
                                        <label htmlFor="description">Description</label>
                                        <Field
                                            as="textarea"
                                            id="description"
                                            name="description"
                                            className="form-textarea"
                                            rows={4}
                                            placeholder="Enter service description"
                                        />
                                        {errors.description && touched.description && (
                                            <div className="text-danger mt-1">{errors.description}</div>
                                        )}
                                    </div>
                                </div>

                                <div className="flex justify-end gap-4 mt-6">
                                    <Link href="/vendor/services" className="btn btn-outline-danger">
                                        Cancel
                                    </Link>
                                    <button type="submit" className="btn btn-primary">
                                        Update Service
                                    </button>
                                </div>
                            </Form>
                        )}
                    </Formik>
                </div>
            </div>
        </div>
    );
};

export default EditService; 