import React, { useEffect, useState } from 'react';
import { Link, router, usePage } from '@inertiajs/react';
import { useDispatch } from 'react-redux';
import Swal from 'sweetalert2';

interface Service {
    id: number;
    name: string;
    duration_minutes: number;
    price: number;
    pivot: {
        allowed_count: number;
    };
}

interface Plan {
    id: number;
    name: string;
    description: string;
    price: number;
    validity_days: number;
    status: string;
    services: Service[];
}

interface Props {
    plans: {
        data: Plan[];
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
    filters: {
        search: string;
        status: string;
        sort: string;
        direction: string;
    };
}

const Index = ({ plans, filters }: Props) => {
    const dispatch = useDispatch();
    const { flash = {}, auth = {} } = usePage<{ flash?: { error?: string; success?: string }, auth?: { user?: { branch?: { currency_symbol?: string; currency_text?: string } } } }>().props;
    const branch = auth.user?.branch || {};
    const currency_symbol = branch.currency_symbol || '₹';
    const currency_text = branch.currency_text || 'INR';
    const [search, setSearch] = useState(filters.search);
    const [status, setStatus] = useState(filters.status);

    // Show SweetAlert for flash error/success
    useEffect(() => {
        if (flash.error) {
            Swal.fire({
                icon: 'error',
                title: flash.error,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
            });
        } else if (flash.success) {
            Swal.fire({
                icon: 'success',
                title: flash.success,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
            });
        }
    }, [flash.error, flash.success]);

    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(
            '/vendor/plans',
            { search: value, status, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleStatusChange = (value: string) => {
        setStatus(value);
        router.get(
            '/vendor/plans',
            { search, status: value, sort: filters.sort, direction: filters.direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleSort = (field: string) => {
        const direction = filters.sort === field && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(
            '/vendor/plans',
            { search, status, sort: field, direction },
            { preserveState: true, preserveScroll: true }
        );
    };

    const handleDelete = (id: number) => {
        Swal.fire({
            title: 'Are you sure?',
            html: 'Are you sure you want to delete this plan?<br>This action cannot be undone.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            padding: '2em',
            customClass: {
                container: 'sweet-alerts'
            }
        }).then((result) => {
            if (result.value) {
                router.delete(`/vendor/plans/${id}`);
            }
        });
    };

    const handleStatusUpdate = (plan: Plan) => {
        const newStatus = plan.status === 'active' ? 'inactive' : 'active';
        router.put(`/vendor/plans/${plan.id}/update-status`, { status: newStatus }, {
            onSuccess: () => {
                Swal.fire({
                    title: 'Updated!',
                    text: 'Plan status has been updated.',
                    icon: 'success',
                    padding: '2em',
                    customClass: {
                        container: 'sweet-alerts'
                    }
                });
            },
            onError: () => {
                Swal.fire({
                    title: 'Error!',
                    text: 'Something went wrong while updating the plan status.',
                    icon: 'error',
                    padding: '2em',
                    customClass: {
                        container: 'sweet-alerts'
                    }
                });
            },
        });
    };

    return (
        <div>
            <ul className="flex space-x-2 rtl:space-x-reverse">
                <li>
                    <Link href="/vendor/dashboard" className="text-primary hover:underline">
                        Dashboard
                    </Link>
                </li>
                <li className="before:content-['/'] ltr:before:mr-2 rtl:before:ml-2">
                    <span>Plans</span>
                </li>
            </ul>

            <div className="pt-5">
                <div className="panel">
                    <div className="flex md:flex-row flex-col md:items-center gap-5 mb-5">
                        <div className="flex flex-1 items-center gap-5">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="py-2 ltr:pr-11 rtl:pl-11 form-input peer"
                                    value={search}
                                    onChange={(e) => handleSearch(e.target.value)}
                                />
                                <button type="button" className="top-1/2 ltr:right-[11px] rtl:left-[11px] absolute peer-focus:text-primary -translate-y-1/2">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="11.5" cy="11.5" r="9.5" stroke="currentColor" strokeWidth="1.5" opacity="0.5"></circle>
                                        <path d="M18.5 18.5L22 22" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"></path>
                                    </svg>
                                </button>
                            </div>
                            <div className="flex items-center gap-5">
                                <select
                                    className="w-32 form-select"
                                    value={status}
                                    onChange={(e) => handleStatusChange(e.target.value)}
                                >
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div className="flex items-center gap-5">
                            <Link href="/vendor/plans/create" className="btn btn-primary">
                                <svg className="ltr:mr-2 rtl:ml-2 w-5 h-5" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                                Add New Plan
                            </Link>
                        </div>
                    </div>

                    <div className="table-responsive">
                        <table className="table-striped">
                            <thead>
                                <tr>
                                    <th onClick={() => handleSort('name')} className="cursor-pointer">
                                        Name {filters.sort === 'name' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th>Description</th>
                                    <th onClick={() => handleSort('price')} className="cursor-pointer">
                                        Price {filters.sort === 'price' && (filters.direction === 'asc' ? '↑' : '↓')}
                                    </th>
                                    <th>Validity</th>
                                    <th>Services</th>
                                    {/* <th>Status</th> */}
                                    <th className="!text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {plans.data.map((plan) => (
                                    <tr key={plan.id}>
                                        <td>{plan.name}</td>
                                        <td>{plan.description}</td>
                                        <td>{currency_symbol}{plan.price} ({currency_text})</td>
                                        <td>{plan.validity_days} days</td>
                                        <td>
                                            <div className="flex flex-wrap gap-1">
                                                {plan.services.map((service) => (
                                                    <span key={service.id} className="badge badge-outline-primary">
                                                        {service.name} ({service.pivot.allowed_count})
                                                    </span>
                                                ))}
                                            </div>
                                        </td>
                                        {/* <td>
                                            <span
                                                className={`badge badge-outline-${
                                                    plan.status === 'active' ? 'success' : 'danger'
                                                }`}
                                            >
                                                {plan.status === 'active' ? 'Active' : 'Inactive'}
                                            </span>
                                        </td> */}
                                        <td className="text-center">
                                            <div className="flex justify-center items-center gap-4">
                                                <button
                                                    type="button"
                                                    className={`btn btn-sm btn-outline-${plan.status === 'active' ? 'danger' : 'success'}`}
                                                    onClick={() => handleStatusUpdate(plan)}
                                                >
                                                    {plan.status === 'active' ? 'Deactivate' : 'Activate'}
                                                </button>
                                                <Link href={`/vendor/plans/${plan.id}/edit`} className="btn btn-sm btn-outline-primary">
                                                    Edit
                                                </Link>
                                                <button type="button" className="btn btn-sm btn-outline-danger" onClick={() => handleDelete(plan.id)}>
                                                    Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Pagination */}
                    <div className="mt-6">
                        <div className="flex flex-wrap justify-center items-center gap-4">
                            {plans.links.map((link, i) => (
                                <Link
                                    key={i}
                                    href={link.url || '#'}
                                    className={`px-4 py-2 text-sm font-semibold rounded-md ${link.active
                                        ? 'bg-primary text-white'
                                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                                        }`}
                                >
                                    {link.label === '&laquo; Previous' ? 'Previous' :
                                        link.label === 'Next &raquo;' ? 'Next' :
                                            link.label}
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Index; 