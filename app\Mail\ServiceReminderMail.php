<?php

declare(strict_types=1);

namespace App\Mail;

use App\Models\AppointmentService;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ServiceReminderMail extends Mailable
{
    use Queueable, SerializesModels;

    public $appointmentService;

    public $user;

    public $service;

    public $branch;

    /**
     * Create a new message instance.
     */
    public function __construct(AppointmentService $appointmentService)
    {
        $this->appointmentService = $appointmentService;
        $this->user               = $appointmentService->appointment->user;
        $this->service            = $appointmentService->service;
        $this->branch             = $appointmentService->appointment->branch;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Reminder: Book Your Next Service Appointment')
            ->view('emails.service_reminder');
    }
}
