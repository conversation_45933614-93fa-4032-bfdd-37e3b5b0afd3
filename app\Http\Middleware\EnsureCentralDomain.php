<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureCentralDomain
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $centralDomains = config('tenancy.central_domains', []);
        $currentDomain  = $request->getHost();

        if (Auth::check()) {

            $user = Auth::user();

            // If not central domain and user is customer, redirect to central domain
            if (! in_array($currentDomain, $centralDomains, true)) {

                if ($user->hasRole('customer')) {
                    $centralDomain = $centralDomains[0] ?? null;

                    if ($centralDomain) {
                        return redirect()->away("https://{$centralDomain}/user/dashboard");
                    }

                    abort(500, 'Central domain not configured properly.');
                }

                abort(404);
            }

        } else {

            // Check if current domain is a central domain
            if (! in_array($currentDomain, $centralDomains)) {
                abort(404);
            }

        }

        return $next($request);
    }
}
