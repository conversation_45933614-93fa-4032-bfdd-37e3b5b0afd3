import { useEffect } from 'react';

const VendorRedirect = ({ redirectUrl,  redirectData }) => {
    useEffect(() => {
        if (redirectUrl) {
            // Redirect after a short delay to allow the page to render
            const timer = setTimeout(() => {
                window.location.href = redirectUrl;
            }, 1000);
            
            return () => clearTimeout(timer);
        }
    }, [redirectUrl]);

    return (
        <div className="flex items-center justify-center min-h-screen bg-gradient-to-r from-indigo-500 to-purple-600">
            <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
                <h1 className="text-2xl font-bold mb-4">{redirectData.section_title}</h1>
                <p className="mb-6">{redirectData.section_subtitle}</p>
                <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-700"></div>
                </div>
                <p className="mt-6 text-sm text-gray-500">
                    If you are not redirected automatically, 
                    <a href={redirectUrl} className="text-indigo-600 hover:underline ml-1">
                        click here
                    </a>
                </p>
            </div>
        </div>
    );
};

export default VendorRedirect;