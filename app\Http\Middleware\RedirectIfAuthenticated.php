<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {

                $user = Auth::guard($guard)->user();

                if ($user->hasRole('vendor')) {
                    return redirect()->route('vendor.home');
                } elseif ($user->hasRole('customer')) {
                    return redirect()->route('user.dashboard');
                } elseif ($user->hasRole('admin')) {
                    return redirect()->route('siteadmin.dashboard');
                } elseif ($user->hasRole('staff')) {
                    return redirect()->route('vendor.appointments.seat-map');
                } elseif ($user->hasRole('branchuser')) {
                    return redirect()->route('vendor.home');
                } else {
                    return redirect()->route('login');
                }
                // return redirect(RouteServiceProvider::HOME);
            }
        }

        return $next($request);
    }
}
