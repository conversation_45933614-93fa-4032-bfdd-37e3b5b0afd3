<?php

namespace App\Services\Notification;

use App\Models\NotificationChannel;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\Mail;

class NotificationService
{
    /**
     * Send an email using the SMTP config from notification_channels table.
     *
     * @param  string|array  $to
     */
    public static function sendMail($to, Mailable $mailable, string $driver = 'smtp'): void
    {
        $channel = NotificationChannel::where('driver', $driver)->where('is_active', true)->first();
        if (! $channel) {
            throw new \Exception('No active SMTP notification channel found.');
        }
        $config = $channel->config;

        // Dynamically set mailer config
        $transportName = 'dynamic_smtp';
        $mailConfig    = [
            'transport'  => 'smtp',
            'host'       => $config['host']       ?? '',
            'port'       => $config['port']       ?? 587,
            'encryption' => $config['encryption'] ?? null,
            'username'   => $config['username']   ?? '',
            'password'   => $config['password']   ?? '',
            'timeout'    => $config['timeout']    ?? null,
            'auth_mode'  => $config['auth_mode']  ?? null,
            'from'       => [
                'address' => $config['from_address'] ?? config('mail.from.address'),
                'name'    => $config['from_name']    ?? config('mail.from.name'),
            ],
        ];

        // Register the dynamic mailer
        app()['config']->set("mail.mailers.{$transportName}", $mailConfig);

        // Send the mail using the dynamic mailer
        Mail::mailer($transportName)->to($to)->send($mailable);
    }
}
