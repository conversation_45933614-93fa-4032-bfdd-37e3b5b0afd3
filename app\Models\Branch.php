<?php

declare(strict_types=1);

namespace App\Models;

use App\Models\Scopes\TenantScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

final class Branch extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'branch_user_id',
        'name',
        'address',
        'email',
        'phone',
        'logo',
        'is_active',
        'social_links',
        'currency_symbol',
        'currency_text',
        'allow_staff',
    ];

    protected $casts = [
        'is_active'    => 'boolean',
        'social_links' => 'array',
        'allow_staff'  => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'branch_id');
    }

    public function appointments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Appointment::class);
    }

    public function seats(): HasMany
    {
        return $this->hasMany(Seat::class);
    }

    public function services(): HasMany
    {
        return $this->hasMany(Service::class);
    }

    protected static function booted(): void
    {
        self::addGlobalScope(new TenantScope);
    }

    public function workingHours()
    {
        return $this->hasOne(WorkingHour::class);
    }
}
