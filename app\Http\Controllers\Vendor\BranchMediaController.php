<?php

declare(strict_types=1);

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\BranchMedia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Inertia\Response;

class BranchMediaController extends Controller
{
    // List all media for a branch
    public function index()
    {
        $userId   = Auth::id();
        $branchId = Auth::user()->current_branch_id;
        $branch   = Branch::where('id', $branchId)->where('user_id', $userId)->firstOrFail();
        $media    = BranchMedia::where('branch_id', $branch->id)->orderByDesc('id')->get();

        return response()->json($media);
    }

    // Store a new media item (image upload)
    public function store(Request $request)
    {
        $userId   = Auth::id();
        $branchId = Auth::user()->current_branch_id;
        $branch   = Branch::where('id', $branchId)->where('user_id', $userId)->firstOrFail();

        $validator = Validator::make($request->all(), [
            'image'  => 'required|image|max:5120', // 5MB
            'type'   => 'required|in:gallery,slider',
            'status' => 'boolean',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $path = $request->file('image')->store('branch_media', 'custom_public');

        $media = BranchMedia::create([
            'branch_id' => $branch->id,
            'image'     => $path,
            'type'      => $request->input('type'),
            'status'    => filter_var($request->input('status', true), FILTER_VALIDATE_BOOLEAN),
        ]);

        return response()->json($media);
    }

    // Update media status
    public function update(Request $request, $mediaId)
    {
        $userId   = Auth::id();
        $branchId = Auth::user()->current_branch_id;
        $branch   = Branch::where('id', $branchId)->where('user_id', $userId)->firstOrFail();
        $media    = BranchMedia::where('id', $mediaId)->where('branch_id', $branch->id)->firstOrFail();

        $validator = Validator::make($request->all(), [
            'status' => 'required|boolean',
        ]);
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $data = [];
        if ($request->has('type')) {
            $data['type'] = $request->input('type');
        }
        if ($request->has('status')) {
            $data['status'] = filter_var($request->input('status'), FILTER_VALIDATE_BOOLEAN);
        }
        $media->update($data);

        return response()->json($media);
    }

    // Delete media
    public function destroy($mediaId)
    {
        $userId   = Auth::id();
        $branchId = Auth::user()->current_branch_id;
        $branch   = Branch::where('id', $branchId)->where('user_id', $userId)->firstOrFail();
        $media    = BranchMedia::where('id', $mediaId)->where('branch_id', $branch->id)->firstOrFail();
        if ($media->image && Storage::disk('custom_public')->exists($media->image)) {
            Storage::disk('custom_public')->delete($media->image);
        }
        $media->forceDelete();

        return response()->json(['message' => 'Media deleted successfully']);
    }

    // Gallery page
    public function galleryPage(): Response
    {
        return Inertia::render('Vendor/Branches/Gallery');
    }

    // Slider page
    public function sliderPage(): Response
    {
        return Inertia::render('Vendor/Branches/Slider');
    }
}
